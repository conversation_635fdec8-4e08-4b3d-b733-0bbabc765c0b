#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
知识库相关的 Schema 定义

基于 RAGFlow API 规范设计的知识库数据模型
简化版本：只保留核心的请求验证 Schema，移除未使用的响应模型
"""
from typing import Any, Dict, List, Optional, Literal

from pydantic import BaseModel, Field


# ParserConfig类已移除，因为RAGFlow规范中parser_config是一个通用的Dict[str, Any]对象
# 具体的配置结构会根据选择的chunk_method而变化


class KnowledgeBaseCreate(BaseModel):
    """创建知识库请求 - 完全符合RAGFlow API规范"""
    name: str = Field(
        ...,
        max_length=65535,
        description="要创建的数据集的唯一名称。它必须遵循以下要求：允许的字符包括英文字母(a-z, A-Z)、数字(0-9)和下划线(_)，必须以英文字母或下划线开头，最多65,535个字符，不区分大小写。"
    )
    avatar: Optional[str] = Field(None, description="头像的Base64编码。")
    description: Optional[str] = Field(None, description="要创建的数据集的简要描述。")
    embedding_model: Optional[str] = Field(
        default=None,  # 不指定默认值，让RAGFlow使用系统默认模型 'text-embedding-bge-m3@LM-Studio'
        description="要使用的嵌入模型的名称。如不指定，系统将自动使用默认模型 'text-embedding-bge-m3@LM-Studio'"
    )
    permission: Optional[Literal["me", "team"]] = Field(
        None,
        description="指定谁可以访问要创建的数据集。可用选项：'me'（默认）：只有您可以管理数据集；'team'：所有团队成员都可以管理数据集。"
    )
    chunk_method: Optional[Literal[
        "naive", "manual", "qa", "table", "paper", "book",
        "laws", "presentation", "picture", "one", "knowledge_graph", "email"
    ]] = Field(
        None,
        description="要创建的数据集的分块方法。"
    )
    parser_config: Optional[Dict[str, Any]] = Field(
        None,
        description="数据集解析器的配置设置。此JSON对象中的属性随所选的'chunk_method'而变化。"
    )


class KnowledgeBaseUpdate(BaseModel):
    """更新知识库请求 - 符合RAGFlow API规范"""
    name: Optional[str] = Field(None, description="数据集的修订名称。")
    embedding_model: Optional[str] = Field(
        None,
        description="更新的嵌入模型名称。在更新'embedding_model'之前，请确保'chunk_count'为0。"
    )
    chunk_method: Optional[Literal[
        "naive", "manual", "qa", "table", "paper", "book",
        "laws", "presentation", "picture", "one", "knowledge_graph", "email"
    ]] = Field(None, description="数据集的分块方法。")


# 注意：响应模型类已移除，直接使用 RAGFlow 返回的数据格式
# 这样可以保持数据的原真性，避免不必要的数据转换


class KnowledgeBaseDelete(BaseModel):
    """删除知识库请求"""
    ids: Optional[List[str]] = Field(None, description="要删除的知识库ID列表，null表示删除所有")


class KnowledgeBaseQuery(BaseModel):
    """知识库查询参数"""
    page: int = Field(default=1, ge=1, description="页码")
    page_size: int = Field(default=30, ge=1, le=100, description="每页大小")
    orderby: str = Field(default="create_time", description="排序字段 (create_time/update_time)")
    desc: bool = Field(default=True, description="是否降序排列")
    name: Optional[str] = Field(None, description="知识库名称过滤")
    id: Optional[str] = Field(None, description="知识库ID过滤")


# 注意：统计信息、响应模型和错误码类已移除
# 原因：
# 1. 统计信息通过专门的统计接口处理，不需要单独的 Schema
# 2. 响应模型统一使用 backend.common.response.response_schema.ResponseModel
# 3. 错误码使用项目统一的错误处理机制，前端有自己的错误码映射
# 4. 保持代码简洁，避免维护未使用的代码
