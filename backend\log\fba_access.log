2025-08-19 08:45:20.164 | INFO     | 04e00b670721482daccfc075be69746b | JWT标准验证成功，获取UUID: 95dcc3ab-4c7b-4dcc-9292-f289ec5bb5ca
2025-08-19 08:45:20.234 | INFO     | 04e00b670721482daccfc075be69746b | 成功认证Java用户: admin
2025-08-19 08:45:20.336 | INFO     | fc06be078da149c1a1c1ddc907d6e98a | JWT标准验证成功，获取UUID: 95dcc3ab-4c7b-4dcc-9292-f289ec5bb5ca
2025-08-19 08:45:20.338 | INFO     | fc06be078da149c1a1c1ddc907d6e98a | 成功认证Java用户: admin
2025-08-19 08:45:20.363 | INFO     | 04e00b670721482daccfc075be69746b | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-19 08:45:20.364 | INFO     | 04e00b670721482daccfc075be69746b | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 202.745ms
2025-08-19 08:45:20.366 | INFO     | 257ad94dfb0d468d9a2cb6eacae1d3af | JWT标准验证成功，获取UUID: 95dcc3ab-4c7b-4dcc-9292-f289ec5bb5ca
2025-08-19 08:45:20.367 | INFO     | 257ad94dfb0d468d9a2cb6eacae1d3af | 成功认证Java用户: admin
2025-08-19 08:45:20.379 | INFO     | fc06be078da149c1a1c1ddc907d6e98a | 获取用户权限成功: user_id=1, is_admin=True, permissions=1
2025-08-19 08:45:20.379 | INFO     | fc06be078da149c1a1c1ddc907d6e98a | 权限检查通过: user_id=1, permission=knowledge:base:list
2025-08-19 08:45:20.385 | INFO     | 257ad94dfb0d468d9a2cb6eacae1d3af | 获取用户权限成功: user_id=1, is_admin=True, permissions=1
2025-08-19 08:45:20.385 | INFO     | 257ad94dfb0d468d9a2cb6eacae1d3af | 权限检查通过: user_id=1, permission=knowledge:base:stats
2025-08-19 08:45:20.395 | INFO     | fc06be078da149c1a1c1ddc907d6e98a | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 08:45:20.397 | INFO     | fc06be078da149c1a1c1ddc907d6e98a | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 60.247ms
2025-08-19 08:45:20.402 | INFO     | 257ad94dfb0d468d9a2cb6eacae1d3af | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 08:45:20.403 | INFO     | 257ad94dfb0d468d9a2cb6eacae1d3af | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 37.105ms
2025-08-19 09:17:47.181 | INFO     | 7ce9179ef6c348509fdacb2d80530c53 | JWT标准验证成功，获取UUID: 95dcc3ab-4c7b-4dcc-9292-f289ec5bb5ca
2025-08-19 09:17:47.185 | INFO     | 7ce9179ef6c348509fdacb2d80530c53 | 成功认证Java用户: admin
2025-08-19 09:17:47.204 | INFO     | 7ce9179ef6c348509fdacb2d80530c53 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-19 09:17:47.205 | INFO     | 7ce9179ef6c348509fdacb2d80530c53 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 24.773ms
2025-08-19 09:17:47.207 | INFO     | 28f3c991db564c228868c9c122e9e18c | JWT标准验证成功，获取UUID: 95dcc3ab-4c7b-4dcc-9292-f289ec5bb5ca
2025-08-19 09:17:47.208 | INFO     | 28f3c991db564c228868c9c122e9e18c | 成功认证Java用户: admin
2025-08-19 09:17:47.213 | INFO     | 28f3c991db564c228868c9c122e9e18c | 获取用户权限成功: user_id=1, is_admin=True, permissions=1
2025-08-19 09:17:47.215 | INFO     | 28f3c991db564c228868c9c122e9e18c | 权限检查通过: user_id=1, permission=knowledge:base:list
2025-08-19 09:17:47.231 | INFO     | 28f3c991db564c228868c9c122e9e18c | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 09:17:47.233 | INFO     | 28f3c991db564c228868c9c122e9e18c | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 26.194ms
2025-08-19 09:17:47.235 | INFO     | 48c4ba93cc094bca9643043d21da528c | JWT标准验证成功，获取UUID: 95dcc3ab-4c7b-4dcc-9292-f289ec5bb5ca
2025-08-19 09:17:47.236 | INFO     | 48c4ba93cc094bca9643043d21da528c | 成功认证Java用户: admin
2025-08-19 09:17:47.241 | INFO     | 48c4ba93cc094bca9643043d21da528c | 获取用户权限成功: user_id=1, is_admin=True, permissions=1
2025-08-19 09:17:47.242 | INFO     | 48c4ba93cc094bca9643043d21da528c | 权限检查通过: user_id=1, permission=knowledge:base:stats
2025-08-19 09:17:47.258 | INFO     | 48c4ba93cc094bca9643043d21da528c | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 09:17:47.260 | INFO     | 48c4ba93cc094bca9643043d21da528c | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 25.069ms
2025-08-19 09:18:09.439 | INFO     | ee7439431baa48389216588e0723ce55 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 09:18:09.440 | INFO     | a8d46183c64347db821ba753617a4ec2 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 09:18:09.441 | INFO     | b0c5da5e6f8242669ec8e550013366e7 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 09:18:09.442 | INFO     | ee7439431baa48389216588e0723ce55 | 成功认证Java用户: pythontest
2025-08-19 09:18:09.445 | INFO     | a8d46183c64347db821ba753617a4ec2 | 成功认证Java用户: pythontest
2025-08-19 09:18:09.445 | INFO     | b0c5da5e6f8242669ec8e550013366e7 | 成功认证Java用户: pythontest
2025-08-19 09:18:09.469 | INFO     | ee7439431baa48389216588e0723ce55 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-19 09:18:09.471 | INFO     | ee7439431baa48389216588e0723ce55 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 32.943ms
2025-08-19 09:18:09.497 | INFO     | b0c5da5e6f8242669ec8e550013366e7 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 09:18:09.498 | INFO     | b0c5da5e6f8242669ec8e550013366e7 | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-19 09:18:09.503 | INFO     | a8d46183c64347db821ba753617a4ec2 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 09:18:09.504 | INFO     | a8d46183c64347db821ba753617a4ec2 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 09:18:09.527 | INFO     | b0c5da5e6f8242669ec8e550013366e7 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 09:18:09.530 | INFO     | a8d46183c64347db821ba753617a4ec2 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 09:18:09.531 | INFO     | b0c5da5e6f8242669ec8e550013366e7 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 90.624ms
2025-08-19 09:18:09.533 | INFO     | a8d46183c64347db821ba753617a4ec2 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 93.464ms
2025-08-19 09:22:23.191 | INFO     | 1cd4dae1bc724c649421f630f686efb2 | 127.0.0.1       | GET      | 200    | /docs | 5.170ms
2025-08-19 09:22:23.308 | INFO     | fd9bffd9c60141029e0df1e3c89b705d | 127.0.0.1       | GET      | 200    | /openapi | 14.164ms
2025-08-19 09:24:42.851 | INFO     | 9dff88302df44db4a2fac28128b6e610 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 09:24:42.853 | INFO     | ad8c12543a024bd88a8fd17724ea9ab3 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 09:24:42.863 | INFO     | fc0e2c3045d44be794ef601d1070d42a | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 09:24:42.867 | INFO     | 9dff88302df44db4a2fac28128b6e610 | 成功认证Java用户: pythontest
2025-08-19 09:24:42.869 | INFO     | ad8c12543a024bd88a8fd17724ea9ab3 | 成功认证Java用户: pythontest
2025-08-19 09:24:42.871 | INFO     | fc0e2c3045d44be794ef601d1070d42a | 成功认证Java用户: pythontest
2025-08-19 09:24:42.886 | INFO     | fc0e2c3045d44be794ef601d1070d42a | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 09:24:42.887 | INFO     | fc0e2c3045d44be794ef601d1070d42a | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-19 09:24:42.890 | INFO     | ad8c12543a024bd88a8fd17724ea9ab3 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 09:24:42.891 | INFO     | ad8c12543a024bd88a8fd17724ea9ab3 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 09:24:42.898 | INFO     | 9dff88302df44db4a2fac28128b6e610 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-19 09:24:42.903 | INFO     | 9dff88302df44db4a2fac28128b6e610 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 51.918ms
2025-08-19 09:24:42.916 | INFO     | fc0e2c3045d44be794ef601d1070d42a | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 09:24:42.918 | INFO     | fc0e2c3045d44be794ef601d1070d42a | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 54.869ms
2025-08-19 09:24:42.920 | INFO     | ad8c12543a024bd88a8fd17724ea9ab3 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 09:24:42.923 | INFO     | ad8c12543a024bd88a8fd17724ea9ab3 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 70.000ms
2025-08-19 09:25:01.670 | INFO     | 1e0348e3f4754fa781af5b3b77e1959e | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 09:25:01.671 | INFO     | 1e0348e3f4754fa781af5b3b77e1959e | 成功认证Java用户: pythontest
2025-08-19 09:25:01.707 | INFO     | 1e0348e3f4754fa781af5b3b77e1959e | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 09:25:01.708 | INFO     | 1e0348e3f4754fa781af5b3b77e1959e | 权限检查通过: user_id=42, permission=knowledge:base:delete
2025-08-19 09:25:02.364 | INFO     | 1e0348e3f4754fa781af5b3b77e1959e | HTTP Request: DELETE http://*************:6610/api/v1/datasets "HTTP/1.1 200 OK"
2025-08-19 09:25:02.374 | INFO     | 1e0348e3f4754fa781af5b3b77e1959e | 127.0.0.1       | DELETE   | 200    | /api/iot/v1/knowledge-base/delete | 704.652ms
2025-08-19 09:25:02.383 | INFO     | db5bd342c9b44c8ca8536e389bc8f86e | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 09:25:02.385 | INFO     | db5bd342c9b44c8ca8536e389bc8f86e | 成功认证Java用户: pythontest
2025-08-19 09:25:02.398 | INFO     | db5bd342c9b44c8ca8536e389bc8f86e | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 09:25:02.399 | INFO     | db5bd342c9b44c8ca8536e389bc8f86e | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 09:25:02.416 | INFO     | db5bd342c9b44c8ca8536e389bc8f86e | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 09:25:02.417 | INFO     | db5bd342c9b44c8ca8536e389bc8f86e | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 34.960ms
2025-08-19 09:25:02.420 | INFO     | ae08a180a54343978153a0d27e2e675a | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 09:25:02.422 | INFO     | ae08a180a54343978153a0d27e2e675a | 成功认证Java用户: pythontest
2025-08-19 09:25:02.428 | INFO     | ae08a180a54343978153a0d27e2e675a | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 09:25:02.430 | INFO     | ae08a180a54343978153a0d27e2e675a | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-19 09:25:02.447 | INFO     | ae08a180a54343978153a0d27e2e675a | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 09:25:02.449 | INFO     | ae08a180a54343978153a0d27e2e675a | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 28.404ms
2025-08-19 09:26:09.292 | INFO     | f8a08a47c6624a6683671069fb53678e | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 09:26:09.294 | INFO     | f8a08a47c6624a6683671069fb53678e | 成功认证Java用户: pythontest
2025-08-19 09:26:09.302 | INFO     | f8a08a47c6624a6683671069fb53678e | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 09:26:09.302 | INFO     | f8a08a47c6624a6683671069fb53678e | 权限检查通过: user_id=42, permission=knowledge:base:delete
2025-08-19 09:26:09.519 | INFO     | f8a08a47c6624a6683671069fb53678e | HTTP Request: DELETE http://*************:6610/api/v1/datasets "HTTP/1.1 200 OK"
2025-08-19 09:26:09.521 | INFO     | f8a08a47c6624a6683671069fb53678e | 127.0.0.1       | DELETE   | 200    | /api/iot/v1/knowledge-base/delete | 228.863ms
2025-08-19 09:26:09.530 | INFO     | 9abb184ab420433db7e751d2416c5ec9 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 09:26:09.532 | INFO     | 9abb184ab420433db7e751d2416c5ec9 | 成功认证Java用户: pythontest
2025-08-19 09:26:09.545 | INFO     | 9abb184ab420433db7e751d2416c5ec9 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 09:26:09.546 | INFO     | 9abb184ab420433db7e751d2416c5ec9 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 09:26:09.559 | INFO     | d74cd1d51b6f4281b81435a0c90d264f | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 09:26:09.560 | INFO     | d74cd1d51b6f4281b81435a0c90d264f | 成功认证Java用户: pythontest
2025-08-19 09:26:09.563 | INFO     | 9abb184ab420433db7e751d2416c5ec9 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 09:26:09.566 | INFO     | 9abb184ab420433db7e751d2416c5ec9 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 35.559ms
2025-08-19 09:26:09.572 | INFO     | d74cd1d51b6f4281b81435a0c90d264f | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 09:26:09.573 | INFO     | d74cd1d51b6f4281b81435a0c90d264f | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-19 09:26:09.591 | INFO     | d74cd1d51b6f4281b81435a0c90d264f | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 09:26:09.592 | INFO     | d74cd1d51b6f4281b81435a0c90d264f | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 33.833ms
2025-08-19 09:26:13.018 | INFO     | 539d643fcfa240eb95978dcaaeb8b972 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 09:26:13.020 | INFO     | 539d643fcfa240eb95978dcaaeb8b972 | 成功认证Java用户: pythontest
2025-08-19 09:26:13.041 | INFO     | 539d643fcfa240eb95978dcaaeb8b972 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 09:26:13.042 | INFO     | 539d643fcfa240eb95978dcaaeb8b972 | 权限检查通过: user_id=42, permission=knowledge:base:create
2025-08-19 09:26:13.085 | INFO     | 539d643fcfa240eb95978dcaaeb8b972 | HTTP Request: POST http://*************:6610/api/v1/datasets "HTTP/1.1 200 OK"
2025-08-19 09:26:13.091 | INFO     | 539d643fcfa240eb95978dcaaeb8b972 | 127.0.0.1       | POST     | 200    | /api/iot/v1/knowledge-base/create | 73.371ms
2025-08-19 09:26:15.589 | INFO     | e6e10b90040d42c79b6162b9e89ad3d2 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 09:26:15.591 | INFO     | e6e10b90040d42c79b6162b9e89ad3d2 | 成功认证Java用户: pythontest
2025-08-19 09:26:15.600 | INFO     | e6e10b90040d42c79b6162b9e89ad3d2 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 09:26:15.600 | INFO     | e6e10b90040d42c79b6162b9e89ad3d2 | 权限检查通过: user_id=42, permission=knowledge:base:create
2025-08-19 09:26:15.617 | INFO     | e6e10b90040d42c79b6162b9e89ad3d2 | HTTP Request: POST http://*************:6610/api/v1/datasets "HTTP/1.1 200 OK"
2025-08-19 09:26:15.619 | INFO     | e6e10b90040d42c79b6162b9e89ad3d2 | 127.0.0.1       | POST     | 200    | /api/iot/v1/knowledge-base/create | 29.357ms
2025-08-19 09:26:16.401 | INFO     | 4f36690724b54637b253f556d65c0ab5 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 09:26:16.412 | INFO     | 4f36690724b54637b253f556d65c0ab5 | 成功认证Java用户: pythontest
2025-08-19 09:26:16.419 | INFO     | 4f36690724b54637b253f556d65c0ab5 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 09:26:16.420 | INFO     | 4f36690724b54637b253f556d65c0ab5 | 权限检查通过: user_id=42, permission=knowledge:base:create
2025-08-19 09:26:16.437 | INFO     | 4f36690724b54637b253f556d65c0ab5 | HTTP Request: POST http://*************:6610/api/v1/datasets "HTTP/1.1 200 OK"
2025-08-19 09:26:16.438 | INFO     | 4f36690724b54637b253f556d65c0ab5 | 127.0.0.1       | POST     | 200    | /api/iot/v1/knowledge-base/create | 38.145ms
2025-08-19 09:26:16.882 | INFO     | 1454eefb1cc641b29c5dbb570bc7d150 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 09:26:16.884 | INFO     | 1454eefb1cc641b29c5dbb570bc7d150 | 成功认证Java用户: pythontest
2025-08-19 09:26:16.893 | INFO     | 1454eefb1cc641b29c5dbb570bc7d150 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 09:26:16.894 | INFO     | 1454eefb1cc641b29c5dbb570bc7d150 | 权限检查通过: user_id=42, permission=knowledge:base:create
2025-08-19 09:26:16.912 | INFO     | 1454eefb1cc641b29c5dbb570bc7d150 | HTTP Request: POST http://*************:6610/api/v1/datasets "HTTP/1.1 200 OK"
2025-08-19 09:26:16.914 | INFO     | 1454eefb1cc641b29c5dbb570bc7d150 | 127.0.0.1       | POST     | 200    | /api/iot/v1/knowledge-base/create | 32.170ms
2025-08-19 09:26:17.320 | INFO     | 9611f50408ee49f3967dfa6df77697c8 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 09:26:17.322 | INFO     | 9611f50408ee49f3967dfa6df77697c8 | 成功认证Java用户: pythontest
2025-08-19 09:26:17.353 | INFO     | 9611f50408ee49f3967dfa6df77697c8 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 09:26:17.354 | INFO     | 9611f50408ee49f3967dfa6df77697c8 | 权限检查通过: user_id=42, permission=knowledge:base:create
2025-08-19 09:26:17.369 | INFO     | 9611f50408ee49f3967dfa6df77697c8 | HTTP Request: POST http://*************:6610/api/v1/datasets "HTTP/1.1 200 OK"
2025-08-19 09:26:17.371 | INFO     | 9611f50408ee49f3967dfa6df77697c8 | 127.0.0.1       | POST     | 200    | /api/iot/v1/knowledge-base/create | 50.312ms
2025-08-19 09:26:17.699 | INFO     | e8cad2c4ff9f4367a890ea6aa67d9d03 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 09:26:17.701 | INFO     | e8cad2c4ff9f4367a890ea6aa67d9d03 | 成功认证Java用户: pythontest
2025-08-19 09:26:17.708 | INFO     | e8cad2c4ff9f4367a890ea6aa67d9d03 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 09:26:17.708 | INFO     | e8cad2c4ff9f4367a890ea6aa67d9d03 | 权限检查通过: user_id=42, permission=knowledge:base:create
2025-08-19 09:26:17.724 | INFO     | e8cad2c4ff9f4367a890ea6aa67d9d03 | HTTP Request: POST http://*************:6610/api/v1/datasets "HTTP/1.1 200 OK"
2025-08-19 09:26:17.726 | INFO     | e8cad2c4ff9f4367a890ea6aa67d9d03 | 127.0.0.1       | POST     | 200    | /api/iot/v1/knowledge-base/create | 27.770ms
2025-08-19 09:26:18.062 | INFO     | 6de0c78153c647109c047ef0f773ebb8 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 09:26:18.063 | INFO     | 6de0c78153c647109c047ef0f773ebb8 | 成功认证Java用户: pythontest
2025-08-19 09:26:18.071 | INFO     | 6de0c78153c647109c047ef0f773ebb8 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 09:26:18.072 | INFO     | 6de0c78153c647109c047ef0f773ebb8 | 权限检查通过: user_id=42, permission=knowledge:base:create
2025-08-19 09:26:18.088 | INFO     | 6de0c78153c647109c047ef0f773ebb8 | HTTP Request: POST http://*************:6610/api/v1/datasets "HTTP/1.1 200 OK"
2025-08-19 09:26:18.090 | INFO     | 6de0c78153c647109c047ef0f773ebb8 | 127.0.0.1       | POST     | 200    | /api/iot/v1/knowledge-base/create | 28.696ms
2025-08-19 09:34:53.194 | INFO     | 46160b1402724e7e963afbd5a469342c | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 09:34:53.198 | INFO     | 46160b1402724e7e963afbd5a469342c | 成功认证Java用户: pythontest
2025-08-19 09:34:53.216 | INFO     | 46160b1402724e7e963afbd5a469342c | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-19 09:34:53.218 | INFO     | 46160b1402724e7e963afbd5a469342c | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 23.621ms
2025-08-19 09:34:53.221 | INFO     | e94e28707cbd465494f0a7ec1012bc05 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 09:34:53.223 | INFO     | e94e28707cbd465494f0a7ec1012bc05 | 成功认证Java用户: pythontest
2025-08-19 09:34:53.229 | INFO     | e94e28707cbd465494f0a7ec1012bc05 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 09:34:53.230 | INFO     | e94e28707cbd465494f0a7ec1012bc05 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 09:34:53.245 | INFO     | e94e28707cbd465494f0a7ec1012bc05 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 09:34:53.247 | INFO     | e94e28707cbd465494f0a7ec1012bc05 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 26.215ms
2025-08-19 09:34:53.250 | INFO     | 412610bb56c747019bbd48f5f109217b | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 09:34:53.251 | INFO     | 412610bb56c747019bbd48f5f109217b | 成功认证Java用户: pythontest
2025-08-19 09:34:53.257 | INFO     | 412610bb56c747019bbd48f5f109217b | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 09:34:53.257 | INFO     | 412610bb56c747019bbd48f5f109217b | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-19 09:34:53.272 | INFO     | 412610bb56c747019bbd48f5f109217b | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 09:34:53.275 | INFO     | 412610bb56c747019bbd48f5f109217b | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 24.890ms
2025-08-19 09:34:55.159 | INFO     | a29207b4b8b54595ab2230df716f7fe7 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 09:34:55.160 | INFO     | 7f9cf82a82e348adba81e70e74fe42c6 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 09:34:55.161 | INFO     | e8b08e9806d84df09ba93e41a5f473cc | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 09:34:55.162 | INFO     | a29207b4b8b54595ab2230df716f7fe7 | 成功认证Java用户: pythontest
2025-08-19 09:34:55.163 | INFO     | 7f9cf82a82e348adba81e70e74fe42c6 | 成功认证Java用户: pythontest
2025-08-19 09:34:55.163 | INFO     | e8b08e9806d84df09ba93e41a5f473cc | 成功认证Java用户: pythontest
2025-08-19 09:34:55.174 | INFO     | e8b08e9806d84df09ba93e41a5f473cc | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 09:34:55.174 | INFO     | e8b08e9806d84df09ba93e41a5f473cc | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-19 09:34:55.180 | INFO     | 7f9cf82a82e348adba81e70e74fe42c6 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 09:34:55.181 | INFO     | 7f9cf82a82e348adba81e70e74fe42c6 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 09:34:55.185 | INFO     | a29207b4b8b54595ab2230df716f7fe7 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-19 09:34:55.190 | INFO     | a29207b4b8b54595ab2230df716f7fe7 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 31.224ms
2025-08-19 09:34:55.205 | INFO     | e8b08e9806d84df09ba93e41a5f473cc | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 09:34:55.206 | INFO     | 7f9cf82a82e348adba81e70e74fe42c6 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 09:34:55.209 | INFO     | e8b08e9806d84df09ba93e41a5f473cc | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 49.339ms
2025-08-19 09:34:55.211 | INFO     | 7f9cf82a82e348adba81e70e74fe42c6 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 51.579ms
2025-08-19 09:51:14.304 | INFO     | b3732553d4834793a2e5634706509557 | 127.0.0.1       | GET      | 200    | /docs | 1.694ms
2025-08-19 09:51:14.414 | INFO     | f5f3128424f74a35ad94dd3518e15fcc | 127.0.0.1       | GET      | 200    | /openapi | 2.734ms
2025-08-19 09:53:52.408 | INFO     | d81ef22231c24c9a9cf58c699643d018 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 09:53:52.409 | INFO     | e4264c7880f24b70946cdf4452f8877e | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 09:53:52.410 | INFO     | d81ef22231c24c9a9cf58c699643d018 | 成功认证Java用户: pythontest
2025-08-19 09:53:52.411 | INFO     | e4264c7880f24b70946cdf4452f8877e | 成功认证Java用户: pythontest
2025-08-19 09:53:52.428 | INFO     | d81ef22231c24c9a9cf58c699643d018 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-19 09:53:52.429 | INFO     | d81ef22231c24c9a9cf58c699643d018 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 21.036ms
2025-08-19 09:53:52.430 | INFO     | e4264c7880f24b70946cdf4452f8877e | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 09:53:52.431 | INFO     | e4264c7880f24b70946cdf4452f8877e | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 09:53:52.434 | INFO     | 309e61e69efd4840a3b16bd19dc7e97f | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 09:53:52.436 | INFO     | 309e61e69efd4840a3b16bd19dc7e97f | 成功认证Java用户: pythontest
2025-08-19 09:53:52.447 | INFO     | e4264c7880f24b70946cdf4452f8877e | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 09:53:52.448 | INFO     | e4264c7880f24b70946cdf4452f8877e | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 39.408ms
2025-08-19 09:53:52.451 | INFO     | 309e61e69efd4840a3b16bd19dc7e97f | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 09:53:52.452 | INFO     | 309e61e69efd4840a3b16bd19dc7e97f | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-19 09:53:52.468 | INFO     | 309e61e69efd4840a3b16bd19dc7e97f | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 09:53:52.469 | INFO     | 309e61e69efd4840a3b16bd19dc7e97f | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 35.242ms
2025-08-19 09:54:02.741 | INFO     | 96c5fd0788ad4c32b4c7621259f329db | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 09:54:02.742 | INFO     | 37dc68e23fc849e0824fc1eb01388181 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 09:54:02.743 | INFO     | c53cab3a96df4e46919e66a937064908 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 09:54:02.743 | INFO     | 96c5fd0788ad4c32b4c7621259f329db | 成功认证Java用户: pythontest
2025-08-19 09:54:02.744 | INFO     | 37dc68e23fc849e0824fc1eb01388181 | 成功认证Java用户: pythontest
2025-08-19 09:54:02.745 | INFO     | c53cab3a96df4e46919e66a937064908 | 成功认证Java用户: pythontest
2025-08-19 09:54:02.756 | INFO     | c53cab3a96df4e46919e66a937064908 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 09:54:02.757 | INFO     | c53cab3a96df4e46919e66a937064908 | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-19 09:54:02.760 | INFO     | 37dc68e23fc849e0824fc1eb01388181 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 09:54:02.761 | INFO     | 37dc68e23fc849e0824fc1eb01388181 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 09:54:02.764 | INFO     | 96c5fd0788ad4c32b4c7621259f329db | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-19 09:54:02.766 | INFO     | 96c5fd0788ad4c32b4c7621259f329db | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 25.040ms
2025-08-19 09:54:02.781 | INFO     | c53cab3a96df4e46919e66a937064908 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 09:54:02.781 | INFO     | 37dc68e23fc849e0824fc1eb01388181 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 09:54:02.783 | INFO     | c53cab3a96df4e46919e66a937064908 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 40.261ms
2025-08-19 09:54:02.784 | INFO     | 37dc68e23fc849e0824fc1eb01388181 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 41.640ms
2025-08-19 09:54:14.748 | INFO     | a9412d5a0405487a81e27091da68ba37 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 09:54:14.749 | INFO     | 4c6707431ce74c6686e14246a017501e | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 09:54:14.749 | INFO     | cb54a70e95ae4bac83ccc3b6a3e6de78 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 09:54:14.750 | INFO     | a9412d5a0405487a81e27091da68ba37 | 成功认证Java用户: pythontest
2025-08-19 09:54:14.750 | INFO     | 4c6707431ce74c6686e14246a017501e | 成功认证Java用户: pythontest
2025-08-19 09:54:14.751 | INFO     | cb54a70e95ae4bac83ccc3b6a3e6de78 | 成功认证Java用户: pythontest
2025-08-19 09:54:14.759 | INFO     | cb54a70e95ae4bac83ccc3b6a3e6de78 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 09:54:14.760 | INFO     | cb54a70e95ae4bac83ccc3b6a3e6de78 | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-19 09:54:14.764 | INFO     | 4c6707431ce74c6686e14246a017501e | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 09:54:14.764 | INFO     | 4c6707431ce74c6686e14246a017501e | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 09:54:14.767 | INFO     | a9412d5a0405487a81e27091da68ba37 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-19 09:54:14.772 | INFO     | a9412d5a0405487a81e27091da68ba37 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 23.874ms
2025-08-19 09:54:14.786 | INFO     | cb54a70e95ae4bac83ccc3b6a3e6de78 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 09:54:14.787 | INFO     | 4c6707431ce74c6686e14246a017501e | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 09:54:14.789 | INFO     | cb54a70e95ae4bac83ccc3b6a3e6de78 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 40.046ms
2025-08-19 09:54:14.790 | INFO     | 4c6707431ce74c6686e14246a017501e | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 41.062ms
2025-08-19 09:55:03.695 | INFO     | - | ✅ Java系统数据库连接创建成功
2025-08-19 09:55:30.209 | INFO     | 13eb16759a624c1191d4a412839776bd | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 09:55:30.212 | INFO     | 13eb16759a624c1191d4a412839776bd | 成功认证Java用户: pythontest
2025-08-19 09:55:30.238 | INFO     | 13eb16759a624c1191d4a412839776bd | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 09:55:30.239 | INFO     | 13eb16759a624c1191d4a412839776bd | 权限检查通过: user_id=42, permission=knowledge:base:create
2025-08-19 09:55:30.283 | INFO     | 13eb16759a624c1191d4a412839776bd | HTTP Request: POST http://*************:6610/api/v1/datasets "HTTP/1.1 200 OK"
2025-08-19 09:55:30.286 | INFO     | 13eb16759a624c1191d4a412839776bd | 127.0.0.1       | POST     | 200    | /api/iot/v1/knowledge-base | 87.159ms
2025-08-19 09:55:33.647 | INFO     | e940de79087346ed9c25cee46dca8bd0 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 09:55:33.650 | INFO     | e940de79087346ed9c25cee46dca8bd0 | 成功认证Java用户: pythontest
2025-08-19 09:55:33.672 | INFO     | e940de79087346ed9c25cee46dca8bd0 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 09:55:33.674 | INFO     | e940de79087346ed9c25cee46dca8bd0 | 权限检查通过: user_id=42, permission=knowledge:base:create
2025-08-19 09:55:33.690 | INFO     | e940de79087346ed9c25cee46dca8bd0 | HTTP Request: POST http://*************:6610/api/v1/datasets "HTTP/1.1 200 OK"
2025-08-19 09:55:33.691 | INFO     | e940de79087346ed9c25cee46dca8bd0 | 127.0.0.1       | POST     | 200    | /api/iot/v1/knowledge-base | 44.011ms
2025-08-19 09:57:30.860 | INFO     | ef34fdd0987a4bd6997c1c0c70f99672 | 127.0.0.1       | GET      | 404    | / | 4.578ms
2025-08-19 09:57:30.868 | INFO     | acd14f4ccd24456c97b1c6b1f3c36138 | 127.0.0.1       | GET      | 200    | /docs | 0.927ms
2025-08-19 09:57:31.255 | INFO     | bcd3265291f84c3481ee227df1d68e68 | 127.0.0.1       | GET      | 404    | /openapi.json | 1.241ms
2025-08-19 09:57:31.668 | INFO     | 8c21b9353197482badebcde8a17ed3d1 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-19 09:57:31.669 | INFO     | 8c21b9353197482badebcde8a17ed3d1 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 21.165ms
2025-08-19 09:58:36.901 | INFO     | be84aaeed52742d591b07e03679cbb8c | 127.0.0.1       | GET      | 404    | / | 1.587ms
2025-08-19 09:58:36.910 | INFO     | 7c09858b8ff944ea820c146985afe6d2 | 127.0.0.1       | GET      | 200    | /docs | 1.253ms
2025-08-19 09:58:37.319 | INFO     | 95d22fc02ba946c1af75e031a677b456 | 127.0.0.1       | GET      | 404    | /openapi.json | 1.331ms
2025-08-19 09:58:37.723 | INFO     | e6e4f468cfda4e899bb9a4465f7e9648 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-19 09:58:37.724 | INFO     | e6e4f468cfda4e899bb9a4465f7e9648 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 13.484ms
2025-08-19 10:00:59.506 | INFO     | 90f54831e7104fb6b8371e7f80e3f3c4 | JWT标准验证成功，获取UUID: fdf903e2-f721-4db1-9617-0be331871b9c
2025-08-19 10:00:59.506 | INFO     | c380c088aa2646fcbc5f1bfb35745d6d | JWT标准验证成功，获取UUID: fdf903e2-f721-4db1-9617-0be331871b9c
2025-08-19 10:00:59.509 | INFO     | d9a6ab7fafe34f1a86fd6c5e924a3dbf | JWT标准验证成功，获取UUID: fdf903e2-f721-4db1-9617-0be331871b9c
2025-08-19 10:00:59.516 | INFO     | 90f54831e7104fb6b8371e7f80e3f3c4 | 成功认证Java用户: admin
2025-08-19 10:00:59.519 | INFO     | c380c088aa2646fcbc5f1bfb35745d6d | 成功认证Java用户: admin
2025-08-19 10:00:59.520 | INFO     | d9a6ab7fafe34f1a86fd6c5e924a3dbf | 成功认证Java用户: admin
2025-08-19 10:00:59.535 | INFO     | d9a6ab7fafe34f1a86fd6c5e924a3dbf | 获取用户权限成功: user_id=1, is_admin=True, permissions=1
2025-08-19 10:00:59.535 | INFO     | d9a6ab7fafe34f1a86fd6c5e924a3dbf | 权限检查通过: user_id=1, permission=knowledge:base:stats
2025-08-19 10:00:59.544 | INFO     | 90f54831e7104fb6b8371e7f80e3f3c4 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-19 10:00:59.546 | INFO     | c380c088aa2646fcbc5f1bfb35745d6d | 获取用户权限成功: user_id=1, is_admin=True, permissions=1
2025-08-19 10:00:59.547 | INFO     | c380c088aa2646fcbc5f1bfb35745d6d | 权限检查通过: user_id=1, permission=knowledge:base:list
2025-08-19 10:00:59.551 | INFO     | 90f54831e7104fb6b8371e7f80e3f3c4 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 45.916ms
2025-08-19 10:00:59.553 | INFO     | d9a6ab7fafe34f1a86fd6c5e924a3dbf | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 10:00:59.556 | INFO     | d9a6ab7fafe34f1a86fd6c5e924a3dbf | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 48.472ms
2025-08-19 10:00:59.566 | INFO     | c380c088aa2646fcbc5f1bfb35745d6d | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 10:00:59.568 | INFO     | c380c088aa2646fcbc5f1bfb35745d6d | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 61.852ms
2025-08-19 10:03:00.695 | INFO     | 68ad47398d0c4d3bbb89fc73e859d0bb | 127.0.0.1       | GET      | 200    | /docs | 1.824ms
2025-08-19 10:03:00.836 | INFO     | bff676658400494c8dcbb987441cf30a | 127.0.0.1       | GET      | 200    | /openapi | 73.428ms
2025-08-19 10:04:28.252 | INFO     | b2c8cdd7ea1a44438ea7badff86baea7 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-19 10:04:28.253 | INFO     | b2c8cdd7ea1a44438ea7badff86baea7 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 14.128ms
2025-08-19 10:04:28.262 | INFO     | 7e8aec06610e4139b3fdffca0f449f74 | 127.0.0.1       | POST     | 401    | /api/iot/v1/knowledge-base | 1.685ms
2025-08-19 10:04:28.272 | INFO     | 4ca54b44405f4e1c93c11ed753fa1365 | 127.0.0.1       | POST     | 401    | /api/iot/v1/knowledge-base/create | 2.296ms
2025-08-19 10:04:28.281 | INFO     | 49894b4fb7314dd698ad298d903eeb5d | 127.0.0.1       | GET      | 401    | /api/iot/v1/knowledge-base/list | 1.434ms
2025-08-19 10:04:28.289 | INFO     | 37a914aa425f46b1aec5a9567f571ba2 | 127.0.0.1       | GET      | 401    | /api/iot/v1/knowledge-base/stats/overview | 0.994ms
2025-08-19 10:04:28.671 | INFO     | d1292390353d4ed892e33847aeffc6ae | 127.0.0.1       | POST     | 401    | /api/iot/v1/knowledge-base | 1.888ms
2025-08-19 10:04:28.679 | INFO     | fe1d180657cd4b1585f5658466fdd6c3 | 127.0.0.1       | POST     | 401    | /api/iot/v1/knowledge-base | 1.656ms
2025-08-19 10:04:28.688 | INFO     | 2417a72c84034d239ecec0db0268c372 | 127.0.0.1       | POST     | 401    | /api/iot/v1/knowledge-base | 2.035ms
2025-08-19 10:04:28.697 | INFO     | c78a3a13e4564abaadbb9af0c554738b | 127.0.0.1       | POST     | 401    | /api/iot/v1/knowledge-base | 1.836ms
2025-08-19 10:04:29.089 | INFO     | 74e29af6f9f14ee8996ffba7f73d01fc | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-19 10:04:29.090 | INFO     | 74e29af6f9f14ee8996ffba7f73d01fc | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 13.290ms
2025-08-19 10:08:03.743 | INFO     | a19549a6c22244b2afc5c9786e267e15 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 10:08:03.745 | INFO     | a19549a6c22244b2afc5c9786e267e15 | 成功认证Java用户: pythontest
2025-08-19 10:08:03.753 | INFO     | a19549a6c22244b2afc5c9786e267e15 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 10:08:03.754 | INFO     | a19549a6c22244b2afc5c9786e267e15 | 权限检查通过: user_id=42, permission=knowledge:base:create
2025-08-19 10:08:03.772 | INFO     | a19549a6c22244b2afc5c9786e267e15 | HTTP Request: POST http://*************:6610/api/v1/datasets "HTTP/1.1 200 OK"
2025-08-19 10:08:03.773 | INFO     | a19549a6c22244b2afc5c9786e267e15 | 127.0.0.1       | POST     | 200    | /api/iot/v1/knowledge-base | 31.481ms
2025-08-19 10:09:35.797 | INFO     | 8ebdd270f1a24ef585e65f63090926ef | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 10:09:35.798 | INFO     | a6e69780a9b845d2ad274a83cf2c584c | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 10:09:35.798 | INFO     | 113cb6daa34547da9edf68a8d0f3f82a | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 10:09:35.801 | INFO     | 8ebdd270f1a24ef585e65f63090926ef | 成功认证Java用户: pythontest
2025-08-19 10:09:35.802 | INFO     | a6e69780a9b845d2ad274a83cf2c584c | 成功认证Java用户: pythontest
2025-08-19 10:09:35.802 | INFO     | 113cb6daa34547da9edf68a8d0f3f82a | 成功认证Java用户: pythontest
2025-08-19 10:09:35.811 | INFO     | a6e69780a9b845d2ad274a83cf2c584c | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 10:09:35.811 | INFO     | a6e69780a9b845d2ad274a83cf2c584c | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 10:09:35.813 | INFO     | 113cb6daa34547da9edf68a8d0f3f82a | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 10:09:35.814 | INFO     | 113cb6daa34547da9edf68a8d0f3f82a | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-19 10:09:35.818 | INFO     | 8ebdd270f1a24ef585e65f63090926ef | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-19 10:09:35.820 | INFO     | 8ebdd270f1a24ef585e65f63090926ef | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 22.055ms
2025-08-19 10:09:35.833 | INFO     | a6e69780a9b845d2ad274a83cf2c584c | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 10:09:35.835 | INFO     | 113cb6daa34547da9edf68a8d0f3f82a | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 10:09:35.835 | INFO     | a6e69780a9b845d2ad274a83cf2c584c | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 37.543ms
2025-08-19 10:09:35.836 | INFO     | 113cb6daa34547da9edf68a8d0f3f82a | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 37.658ms
2025-08-19 10:10:02.028 | INFO     | - | ✅ Java系统数据库连接创建成功
2025-08-19 10:10:11.592 | INFO     | f66faea702b8491cb9e1c554956ac068 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 10:10:11.594 | INFO     | f66faea702b8491cb9e1c554956ac068 | 成功认证Java用户: pythontest
2025-08-19 10:10:11.616 | INFO     | f66faea702b8491cb9e1c554956ac068 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 10:10:11.617 | INFO     | f66faea702b8491cb9e1c554956ac068 | 权限检查通过: user_id=42, permission=knowledge:base:create
2025-08-19 10:10:11.652 | INFO     | f66faea702b8491cb9e1c554956ac068 | HTTP Request: POST http://*************:6610/api/v1/datasets "HTTP/1.1 200 OK"
2025-08-19 10:10:11.655 | INFO     | f66faea702b8491cb9e1c554956ac068 | 127.0.0.1       | POST     | 200    | /api/iot/v1/knowledge-base | 67.152ms
2025-08-19 10:14:19.031 | INFO     | 4c7a74df74234740991b0e94d93a00e7 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 10:14:19.033 | INFO     | 4c7a74df74234740991b0e94d93a00e7 | 成功认证Java用户: pythontest
2025-08-19 10:14:19.047 | INFO     | 75470ce3df284e95a9941b4a50d5544a | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 10:14:19.047 | INFO     | 404755bc715d4c0a9ccc661950728fbc | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 10:14:19.048 | INFO     | 4c7a74df74234740991b0e94d93a00e7 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-19 10:14:19.049 | INFO     | 75470ce3df284e95a9941b4a50d5544a | 成功认证Java用户: pythontest
2025-08-19 10:14:19.049 | INFO     | 4c7a74df74234740991b0e94d93a00e7 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 19.143ms
2025-08-19 10:14:19.052 | INFO     | 404755bc715d4c0a9ccc661950728fbc | 成功认证Java用户: pythontest
2025-08-19 10:14:19.072 | INFO     | 404755bc715d4c0a9ccc661950728fbc | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 10:14:19.072 | INFO     | 404755bc715d4c0a9ccc661950728fbc | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-19 10:14:19.083 | INFO     | 75470ce3df284e95a9941b4a50d5544a | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 10:14:19.084 | INFO     | 75470ce3df284e95a9941b4a50d5544a | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 10:14:19.087 | INFO     | 404755bc715d4c0a9ccc661950728fbc | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 10:14:19.088 | INFO     | 404755bc715d4c0a9ccc661950728fbc | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 40.702ms
2025-08-19 10:14:19.098 | INFO     | 75470ce3df284e95a9941b4a50d5544a | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 10:14:19.099 | INFO     | 75470ce3df284e95a9941b4a50d5544a | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 52.420ms
2025-08-19 10:18:02.036 | INFO     | - | ✅ Java系统数据库连接创建成功
2025-08-19 10:18:12.560 | INFO     | 1b269c803ffe44e7a3425340bc7a9b5c | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 10:18:12.561 | INFO     | afe7c9198a864ca49568689bdcf72dc1 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 10:18:12.563 | INFO     | c8120564228d4d029f3a2ffc268b1b97 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 10:18:12.563 | INFO     | 1b269c803ffe44e7a3425340bc7a9b5c | 成功认证Java用户: pythontest
2025-08-19 10:18:12.566 | INFO     | afe7c9198a864ca49568689bdcf72dc1 | 成功认证Java用户: pythontest
2025-08-19 10:18:12.566 | INFO     | c8120564228d4d029f3a2ffc268b1b97 | 成功认证Java用户: pythontest
2025-08-19 10:18:12.594 | INFO     | c8120564228d4d029f3a2ffc268b1b97 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 10:18:12.594 | INFO     | c8120564228d4d029f3a2ffc268b1b97 | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-19 10:18:12.597 | INFO     | afe7c9198a864ca49568689bdcf72dc1 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 10:18:12.598 | INFO     | afe7c9198a864ca49568689bdcf72dc1 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 10:18:12.599 | INFO     | 1b269c803ffe44e7a3425340bc7a9b5c | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-19 10:18:12.602 | INFO     | 1b269c803ffe44e7a3425340bc7a9b5c | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 45.436ms
2025-08-19 10:18:12.618 | INFO     | c8120564228d4d029f3a2ffc268b1b97 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 10:18:12.619 | INFO     | afe7c9198a864ca49568689bdcf72dc1 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 10:18:12.620 | INFO     | c8120564228d4d029f3a2ffc268b1b97 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 59.431ms
2025-08-19 10:18:12.621 | INFO     | afe7c9198a864ca49568689bdcf72dc1 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 60.531ms
2025-08-19 10:18:20.542 | INFO     | 5715bfb937aa4aa4896560d635fc75ff | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 10:18:20.544 | INFO     | 5715bfb937aa4aa4896560d635fc75ff | 成功认证Java用户: pythontest
2025-08-19 10:18:20.561 | INFO     | 5715bfb937aa4aa4896560d635fc75ff | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 10:18:20.562 | INFO     | 5715bfb937aa4aa4896560d635fc75ff | 权限检查通过: user_id=42, permission=knowledge:base:create
2025-08-19 10:18:20.562 | INFO     | 5715bfb937aa4aa4896560d635fc75ff | 创建知识库请求数据: {'name': 'pythontest3', 'description': '', 'embedding_model': 'bge-m3:latest@Ollama', 'permission': 'me', 'chunk_method': 'naive', 'parser_config': {'chunk_token_num': 128, 'delimiter': '\\n!?;。；！？', 'html4excel': False, 'layout_recognize': 'DeepDOC', 'auto_keywords': 0, 'auto_questions': 0, 'task_page_size': 12}}
2025-08-19 10:18:20.579 | INFO     | 5715bfb937aa4aa4896560d635fc75ff | HTTP Request: POST http://*************:6610/api/v1/datasets "HTTP/1.1 200 OK"
2025-08-19 10:18:20.580 | INFO     | 5715bfb937aa4aa4896560d635fc75ff | 尝试使用备用模型: BAAI/bge-zh-v1.5
2025-08-19 10:18:20.593 | INFO     | 5715bfb937aa4aa4896560d635fc75ff | HTTP Request: POST http://*************:6610/api/v1/datasets "HTTP/1.1 200 OK"
2025-08-19 10:18:20.594 | INFO     | 5715bfb937aa4aa4896560d635fc75ff | 尝试使用备用模型: BAAI/bge-large-zh-v1.5
2025-08-19 10:18:20.605 | INFO     | 5715bfb937aa4aa4896560d635fc75ff | HTTP Request: POST http://*************:6610/api/v1/datasets "HTTP/1.1 200 OK"
2025-08-19 10:18:20.606 | INFO     | 5715bfb937aa4aa4896560d635fc75ff | 尝试使用备用模型: text-embedding-ada-002
2025-08-19 10:18:20.617 | INFO     | 5715bfb937aa4aa4896560d635fc75ff | HTTP Request: POST http://*************:6610/api/v1/datasets "HTTP/1.1 200 OK"
2025-08-19 10:18:20.618 | INFO     | 5715bfb937aa4aa4896560d635fc75ff | 127.0.0.1       | POST     | 200    | /api/iot/v1/knowledge-base | 77.087ms
2025-08-19 11:37:17.149 | INFO     | - | ✅ Java系统数据库连接创建成功
2025-08-19 11:37:40.228 | INFO     | f36e48582d2b4f7c9c426103078ca456 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 11:37:40.230 | INFO     | a329fadeb4604146b034da10960c1c8d | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 11:37:40.231 | INFO     | f36e48582d2b4f7c9c426103078ca456 | 成功认证Java用户: pythontest
2025-08-19 11:37:40.233 | INFO     | a329fadeb4604146b034da10960c1c8d | 成功认证Java用户: pythontest
2025-08-19 11:37:40.312 | INFO     | f36e48582d2b4f7c9c426103078ca456 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-19 11:37:40.314 | INFO     | f36e48582d2b4f7c9c426103078ca456 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 93.387ms
2025-08-19 11:37:40.316 | INFO     | a329fadeb4604146b034da10960c1c8d | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 11:37:40.317 | INFO     | a329fadeb4604146b034da10960c1c8d | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 11:37:40.322 | INFO     | f02fcf8e76eb455884838242cacdcaeb | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 11:37:40.324 | INFO     | f02fcf8e76eb455884838242cacdcaeb | 成功认证Java用户: pythontest
2025-08-19 11:37:40.343 | INFO     | a329fadeb4604146b034da10960c1c8d | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 11:37:40.348 | INFO     | a329fadeb4604146b034da10960c1c8d | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 117.754ms
2025-08-19 11:37:40.351 | INFO     | f02fcf8e76eb455884838242cacdcaeb | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 11:37:40.352 | INFO     | f02fcf8e76eb455884838242cacdcaeb | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-19 11:37:40.369 | INFO     | f02fcf8e76eb455884838242cacdcaeb | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 11:37:40.372 | INFO     | f02fcf8e76eb455884838242cacdcaeb | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 50.036ms
2025-08-19 11:37:44.760 | INFO     | ba10d882517b49079a7caf863f7ce557 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 11:37:44.761 | INFO     | ba10d882517b49079a7caf863f7ce557 | 成功认证Java用户: pythontest
2025-08-19 11:37:44.772 | INFO     | ba10d882517b49079a7caf863f7ce557 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 11:37:44.773 | INFO     | ba10d882517b49079a7caf863f7ce557 | 权限检查通过: user_id=42, permission=knowledge:base:create
2025-08-19 11:37:44.774 | INFO     | ba10d882517b49079a7caf863f7ce557 | 创建知识库请求数据: {'name': 'pythontest3', 'description': '', 'embedding_model': 'bge-m3:latest@Ollama', 'permission': 'me', 'chunk_method': 'naive', 'parser_config': {'chunk_token_num': 128, 'delimiter': '\\n!?;。；！？', 'html4excel': False, 'layout_recognize': 'DeepDOC', 'auto_keywords': 0, 'auto_questions': 0, 'task_page_size': 12}}
2025-08-19 11:37:44.796 | INFO     | ba10d882517b49079a7caf863f7ce557 | HTTP Request: POST http://*************:6610/api/v1/datasets "HTTP/1.1 200 OK"
2025-08-19 11:37:44.798 | INFO     | ba10d882517b49079a7caf863f7ce557 | 尝试使用备用模型: BAAI/bge-zh-v1.5
2025-08-19 11:37:44.811 | INFO     | ba10d882517b49079a7caf863f7ce557 | HTTP Request: POST http://*************:6610/api/v1/datasets "HTTP/1.1 200 OK"
2025-08-19 11:37:44.813 | INFO     | ba10d882517b49079a7caf863f7ce557 | 尝试使用备用模型: BAAI/bge-large-zh-v1.5
2025-08-19 11:37:44.828 | INFO     | ba10d882517b49079a7caf863f7ce557 | HTTP Request: POST http://*************:6610/api/v1/datasets "HTTP/1.1 200 OK"
2025-08-19 11:37:44.830 | INFO     | ba10d882517b49079a7caf863f7ce557 | 尝试使用备用模型: text-embedding-ada-002
2025-08-19 11:37:44.844 | INFO     | ba10d882517b49079a7caf863f7ce557 | HTTP Request: POST http://*************:6610/api/v1/datasets "HTTP/1.1 200 OK"
2025-08-19 11:37:44.847 | INFO     | ba10d882517b49079a7caf863f7ce557 | 127.0.0.1       | POST     | 200    | /api/iot/v1/knowledge-base | 87.167ms
2025-08-19 11:39:25.984 | INFO     | 697d04c6295e4a18ab179af9f58fa6a2 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 11:39:25.986 | INFO     | 697d04c6295e4a18ab179af9f58fa6a2 | 成功认证Java用户: pythontest
2025-08-19 11:39:25.991 | INFO     | 697d04c6295e4a18ab179af9f58fa6a2 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 11:39:25.991 | INFO     | 697d04c6295e4a18ab179af9f58fa6a2 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 11:39:26.007 | INFO     | 697d04c6295e4a18ab179af9f58fa6a2 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 11:39:26.009 | INFO     | 697d04c6295e4a18ab179af9f58fa6a2 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 25.479ms
2025-08-19 11:39:37.361 | INFO     | 1d437b9cab264b9eb3e8d42bf8482548 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 11:39:37.362 | INFO     | 1d437b9cab264b9eb3e8d42bf8482548 | 成功认证Java用户: pythontest
2025-08-19 11:39:37.380 | INFO     | 1d437b9cab264b9eb3e8d42bf8482548 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-19 11:39:37.382 | INFO     | 1d437b9cab264b9eb3e8d42bf8482548 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 21.119ms
2025-08-19 11:39:37.384 | INFO     | 3a09174156744f71bbf9623784688c36 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 11:39:37.385 | INFO     | 3a09174156744f71bbf9623784688c36 | 成功认证Java用户: pythontest
2025-08-19 11:39:37.392 | INFO     | 3a09174156744f71bbf9623784688c36 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 11:39:37.392 | INFO     | 3a09174156744f71bbf9623784688c36 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 11:39:37.408 | INFO     | 3a09174156744f71bbf9623784688c36 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 11:39:37.410 | INFO     | 3a09174156744f71bbf9623784688c36 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 26.403ms
2025-08-19 11:39:37.412 | INFO     | 2f300af1489f4c5991ce4f14b7c7d323 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 11:39:37.413 | INFO     | 2f300af1489f4c5991ce4f14b7c7d323 | 成功认证Java用户: pythontest
2025-08-19 11:39:37.419 | INFO     | 2f300af1489f4c5991ce4f14b7c7d323 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 11:39:37.419 | INFO     | 2f300af1489f4c5991ce4f14b7c7d323 | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-19 11:39:37.436 | INFO     | 2f300af1489f4c5991ce4f14b7c7d323 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 11:39:37.438 | INFO     | 2f300af1489f4c5991ce4f14b7c7d323 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 25.887ms
2025-08-19 11:41:20.622 | INFO     | 0b1bffe2eed84ac7887b9e2c0a314f7b | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 11:41:20.623 | INFO     | 36294cee3bd14ecbb153d2fa9e1266a0 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 11:41:20.623 | INFO     | acbd52535d6a494093ed3580973b2b2b | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 11:41:20.624 | INFO     | 0b1bffe2eed84ac7887b9e2c0a314f7b | 成功认证Java用户: pythontest
2025-08-19 11:41:20.624 | INFO     | 36294cee3bd14ecbb153d2fa9e1266a0 | 成功认证Java用户: pythontest
2025-08-19 11:41:20.627 | INFO     | acbd52535d6a494093ed3580973b2b2b | 成功认证Java用户: pythontest
2025-08-19 11:41:20.634 | INFO     | acbd52535d6a494093ed3580973b2b2b | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 11:41:20.635 | INFO     | acbd52535d6a494093ed3580973b2b2b | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-19 11:41:20.643 | INFO     | 36294cee3bd14ecbb153d2fa9e1266a0 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 11:41:20.644 | INFO     | 36294cee3bd14ecbb153d2fa9e1266a0 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 11:41:20.647 | INFO     | 0b1bffe2eed84ac7887b9e2c0a314f7b | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-19 11:41:20.649 | INFO     | 0b1bffe2eed84ac7887b9e2c0a314f7b | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 27.762ms
2025-08-19 11:41:20.652 | INFO     | acbd52535d6a494093ed3580973b2b2b | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 11:41:20.653 | INFO     | acbd52535d6a494093ed3580973b2b2b | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 30.156ms
2025-08-19 11:41:20.662 | INFO     | 36294cee3bd14ecbb153d2fa9e1266a0 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 11:41:20.664 | INFO     | 36294cee3bd14ecbb153d2fa9e1266a0 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 41.360ms
2025-08-19 11:46:14.662 | INFO     | c3846d9a6ef34166849faec850bd6feb | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 11:46:14.662 | INFO     | 7de866907eb2420594dbf936f51e6f77 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 11:46:14.663 | INFO     | 7c0b3a484a794719b2a5777ddd516980 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 11:46:14.664 | INFO     | c3846d9a6ef34166849faec850bd6feb | 成功认证Java用户: pythontest
2025-08-19 11:46:14.665 | INFO     | 7de866907eb2420594dbf936f51e6f77 | 成功认证Java用户: pythontest
2025-08-19 11:46:14.665 | INFO     | 7c0b3a484a794719b2a5777ddd516980 | 成功认证Java用户: pythontest
2025-08-19 11:46:14.673 | INFO     | 7c0b3a484a794719b2a5777ddd516980 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 11:46:14.674 | INFO     | 7c0b3a484a794719b2a5777ddd516980 | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-19 11:46:14.676 | INFO     | 7de866907eb2420594dbf936f51e6f77 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 11:46:14.677 | INFO     | 7de866907eb2420594dbf936f51e6f77 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 11:46:14.683 | INFO     | c3846d9a6ef34166849faec850bd6feb | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-19 11:46:14.684 | INFO     | c3846d9a6ef34166849faec850bd6feb | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 23.196ms
2025-08-19 11:46:14.698 | INFO     | 7c0b3a484a794719b2a5777ddd516980 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 11:46:14.700 | INFO     | 7c0b3a484a794719b2a5777ddd516980 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 37.197ms
2025-08-19 11:46:14.702 | INFO     | 7de866907eb2420594dbf936f51e6f77 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 11:46:14.703 | INFO     | 7de866907eb2420594dbf936f51e6f77 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 40.537ms
2025-08-19 11:50:22.242 | INFO     | be85c8d9ceb74ab7bffdd1952861c39c | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 11:50:22.244 | INFO     | be85c8d9ceb74ab7bffdd1952861c39c | 成功认证Java用户: pythontest
2025-08-19 11:50:22.262 | INFO     | be85c8d9ceb74ab7bffdd1952861c39c | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-19 11:50:22.265 | INFO     | be85c8d9ceb74ab7bffdd1952861c39c | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 23.275ms
2025-08-19 11:50:22.268 | INFO     | f834e1c88a0944938975f306f5e12284 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 11:50:22.269 | INFO     | f834e1c88a0944938975f306f5e12284 | 成功认证Java用户: pythontest
2025-08-19 11:50:22.276 | INFO     | f834e1c88a0944938975f306f5e12284 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 11:50:22.277 | INFO     | f834e1c88a0944938975f306f5e12284 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 11:50:22.296 | INFO     | f834e1c88a0944938975f306f5e12284 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 11:50:22.298 | INFO     | f834e1c88a0944938975f306f5e12284 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 30.998ms
2025-08-19 11:50:22.300 | INFO     | 3564ba990e2740ccb8546674267b3ba4 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 11:50:22.302 | INFO     | 3564ba990e2740ccb8546674267b3ba4 | 成功认证Java用户: pythontest
2025-08-19 11:50:22.307 | INFO     | 3564ba990e2740ccb8546674267b3ba4 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 11:50:22.308 | INFO     | 3564ba990e2740ccb8546674267b3ba4 | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-19 11:50:22.328 | INFO     | 3564ba990e2740ccb8546674267b3ba4 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 11:50:22.330 | INFO     | 3564ba990e2740ccb8546674267b3ba4 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 29.606ms
2025-08-19 11:50:24.193 | INFO     | 217899791b244ffab05f4f4854146a7e | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 11:50:24.195 | INFO     | 217899791b244ffab05f4f4854146a7e | 成功认证Java用户: pythontest
2025-08-19 11:50:24.203 | INFO     | 217899791b244ffab05f4f4854146a7e | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 11:50:24.204 | INFO     | 217899791b244ffab05f4f4854146a7e | 权限检查通过: user_id=42, permission=knowledge:base:delete
2025-08-19 11:50:24.362 | INFO     | 217899791b244ffab05f4f4854146a7e | HTTP Request: DELETE http://*************:6610/api/v1/datasets "HTTP/1.1 200 OK"
2025-08-19 11:50:24.364 | INFO     | 217899791b244ffab05f4f4854146a7e | 127.0.0.1       | DELETE   | 200    | /api/iot/v1/knowledge-base/delete | 171.203ms
2025-08-19 11:50:24.372 | INFO     | 959349d037bf4c63aaa4d37f52e959e9 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 11:50:24.374 | INFO     | 8e65113621e249af967412ddd7265825 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 11:50:24.375 | INFO     | 959349d037bf4c63aaa4d37f52e959e9 | 成功认证Java用户: pythontest
2025-08-19 11:50:24.377 | INFO     | 8e65113621e249af967412ddd7265825 | 成功认证Java用户: pythontest
2025-08-19 11:50:24.384 | INFO     | 959349d037bf4c63aaa4d37f52e959e9 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 11:50:24.386 | INFO     | 959349d037bf4c63aaa4d37f52e959e9 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 11:50:24.390 | INFO     | 8e65113621e249af967412ddd7265825 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 11:50:24.392 | INFO     | 8e65113621e249af967412ddd7265825 | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-19 11:50:24.416 | INFO     | 959349d037bf4c63aaa4d37f52e959e9 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 11:50:24.418 | INFO     | 8e65113621e249af967412ddd7265825 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 11:50:24.419 | INFO     | 959349d037bf4c63aaa4d37f52e959e9 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 47.078ms
2025-08-19 11:50:24.421 | INFO     | 8e65113621e249af967412ddd7265825 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 47.181ms
2025-08-19 11:50:26.009 | INFO     | 56351bbd564c4c599ecee446c18b2795 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 11:50:26.011 | INFO     | 56351bbd564c4c599ecee446c18b2795 | 成功认证Java用户: pythontest
2025-08-19 11:50:26.018 | INFO     | 56351bbd564c4c599ecee446c18b2795 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 11:50:26.019 | INFO     | 56351bbd564c4c599ecee446c18b2795 | 权限检查通过: user_id=42, permission=knowledge:base:delete
2025-08-19 11:50:26.203 | INFO     | 56351bbd564c4c599ecee446c18b2795 | HTTP Request: DELETE http://*************:6610/api/v1/datasets "HTTP/1.1 200 OK"
2025-08-19 11:50:26.205 | INFO     | 56351bbd564c4c599ecee446c18b2795 | 127.0.0.1       | DELETE   | 200    | /api/iot/v1/knowledge-base/delete | 196.117ms
2025-08-19 11:50:26.219 | INFO     | b7f043efb3ce4b6783410099c98adeca | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 11:50:26.220 | INFO     | 537f4d6d05a747e78ae4f2696224c307 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 11:50:26.224 | INFO     | b7f043efb3ce4b6783410099c98adeca | 成功认证Java用户: pythontest
2025-08-19 11:50:26.224 | INFO     | 537f4d6d05a747e78ae4f2696224c307 | 成功认证Java用户: pythontest
2025-08-19 11:50:26.233 | INFO     | b7f043efb3ce4b6783410099c98adeca | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 11:50:26.234 | INFO     | b7f043efb3ce4b6783410099c98adeca | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 11:50:26.239 | INFO     | 537f4d6d05a747e78ae4f2696224c307 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 11:50:26.240 | INFO     | 537f4d6d05a747e78ae4f2696224c307 | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-19 11:50:26.268 | INFO     | 537f4d6d05a747e78ae4f2696224c307 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 11:50:26.269 | INFO     | b7f043efb3ce4b6783410099c98adeca | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 11:50:26.272 | INFO     | 537f4d6d05a747e78ae4f2696224c307 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 52.264ms
2025-08-19 11:50:26.273 | INFO     | b7f043efb3ce4b6783410099c98adeca | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 54.671ms
2025-08-19 11:50:27.558 | INFO     | 615d6bd105124cacbaaf8b51d5382adc | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 11:50:27.559 | INFO     | 615d6bd105124cacbaaf8b51d5382adc | 成功认证Java用户: pythontest
2025-08-19 11:50:27.567 | INFO     | 615d6bd105124cacbaaf8b51d5382adc | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 11:50:27.569 | INFO     | 615d6bd105124cacbaaf8b51d5382adc | 权限检查通过: user_id=42, permission=knowledge:base:delete
2025-08-19 11:50:27.749 | INFO     | 615d6bd105124cacbaaf8b51d5382adc | HTTP Request: DELETE http://*************:6610/api/v1/datasets "HTTP/1.1 200 OK"
2025-08-19 11:50:27.751 | INFO     | 615d6bd105124cacbaaf8b51d5382adc | 127.0.0.1       | DELETE   | 200    | /api/iot/v1/knowledge-base/delete | 194.380ms
2025-08-19 11:50:27.759 | INFO     | 8f6863eff94a4280b442df207298ed01 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 11:50:27.762 | INFO     | a28f987e4f1f46baad04286697ec5ee5 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 11:50:27.763 | INFO     | 8f6863eff94a4280b442df207298ed01 | 成功认证Java用户: pythontest
2025-08-19 11:50:27.764 | INFO     | a28f987e4f1f46baad04286697ec5ee5 | 成功认证Java用户: pythontest
2025-08-19 11:50:27.773 | INFO     | a28f987e4f1f46baad04286697ec5ee5 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 11:50:27.774 | INFO     | a28f987e4f1f46baad04286697ec5ee5 | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-19 11:50:27.779 | INFO     | 8f6863eff94a4280b442df207298ed01 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 11:50:27.779 | INFO     | 8f6863eff94a4280b442df207298ed01 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 11:50:27.804 | INFO     | a28f987e4f1f46baad04286697ec5ee5 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 11:50:27.805 | INFO     | 8f6863eff94a4280b442df207298ed01 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 11:50:27.808 | INFO     | a28f987e4f1f46baad04286697ec5ee5 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 47.618ms
2025-08-19 11:50:27.810 | INFO     | 8f6863eff94a4280b442df207298ed01 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 51.046ms
2025-08-19 11:50:28.927 | INFO     | 02eff211a95847cb836f34d132ace6f7 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 11:50:28.929 | INFO     | 02eff211a95847cb836f34d132ace6f7 | 成功认证Java用户: pythontest
2025-08-19 11:50:28.937 | INFO     | 02eff211a95847cb836f34d132ace6f7 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 11:50:28.938 | INFO     | 02eff211a95847cb836f34d132ace6f7 | 权限检查通过: user_id=42, permission=knowledge:base:delete
2025-08-19 11:50:29.094 | INFO     | 02eff211a95847cb836f34d132ace6f7 | HTTP Request: DELETE http://*************:6610/api/v1/datasets "HTTP/1.1 200 OK"
2025-08-19 11:50:29.096 | INFO     | 02eff211a95847cb836f34d132ace6f7 | 127.0.0.1       | DELETE   | 200    | /api/iot/v1/knowledge-base/delete | 169.316ms
2025-08-19 11:50:29.103 | INFO     | 7b5efa076f994fe185993a8f3ee0af3e | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 11:50:29.105 | INFO     | 6b1d36515f034388a91698f15ef348ef | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 11:50:29.105 | INFO     | 7b5efa076f994fe185993a8f3ee0af3e | 成功认证Java用户: pythontest
2025-08-19 11:50:29.106 | INFO     | 6b1d36515f034388a91698f15ef348ef | 成功认证Java用户: pythontest
2025-08-19 11:50:29.116 | INFO     | 6b1d36515f034388a91698f15ef348ef | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 11:50:29.116 | INFO     | 6b1d36515f034388a91698f15ef348ef | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-19 11:50:29.125 | INFO     | 7b5efa076f994fe185993a8f3ee0af3e | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 11:50:29.127 | INFO     | 7b5efa076f994fe185993a8f3ee0af3e | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 11:50:29.142 | INFO     | 6b1d36515f034388a91698f15ef348ef | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 11:50:29.144 | INFO     | 6b1d36515f034388a91698f15ef348ef | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 39.998ms
2025-08-19 11:50:29.151 | INFO     | 7b5efa076f994fe185993a8f3ee0af3e | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 11:50:29.153 | INFO     | 7b5efa076f994fe185993a8f3ee0af3e | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 50.339ms
2025-08-19 11:50:30.718 | INFO     | 8b8a87c3d04841409df32af01e0cdbae | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 11:50:30.720 | INFO     | 8b8a87c3d04841409df32af01e0cdbae | 成功认证Java用户: pythontest
2025-08-19 11:50:30.729 | INFO     | 8b8a87c3d04841409df32af01e0cdbae | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 11:50:30.731 | INFO     | 8b8a87c3d04841409df32af01e0cdbae | 权限检查通过: user_id=42, permission=knowledge:base:delete
2025-08-19 11:50:30.862 | INFO     | 8b8a87c3d04841409df32af01e0cdbae | HTTP Request: DELETE http://*************:6610/api/v1/datasets "HTTP/1.1 200 OK"
2025-08-19 11:50:30.864 | INFO     | 8b8a87c3d04841409df32af01e0cdbae | 127.0.0.1       | DELETE   | 200    | /api/iot/v1/knowledge-base/delete | 146.456ms
2025-08-19 11:50:30.872 | INFO     | c715f4017b064540b2dd9d605b390a85 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 11:50:30.874 | INFO     | cd94134640b242ac9482419a2fd883af | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 11:50:30.876 | INFO     | c715f4017b064540b2dd9d605b390a85 | 成功认证Java用户: pythontest
2025-08-19 11:50:30.877 | INFO     | cd94134640b242ac9482419a2fd883af | 成功认证Java用户: pythontest
2025-08-19 11:50:30.885 | INFO     | cd94134640b242ac9482419a2fd883af | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 11:50:30.886 | INFO     | cd94134640b242ac9482419a2fd883af | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-19 11:50:30.891 | INFO     | c715f4017b064540b2dd9d605b390a85 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 11:50:30.892 | INFO     | c715f4017b064540b2dd9d605b390a85 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 11:50:30.919 | INFO     | cd94134640b242ac9482419a2fd883af | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 11:50:30.922 | INFO     | c715f4017b064540b2dd9d605b390a85 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 11:50:30.924 | INFO     | cd94134640b242ac9482419a2fd883af | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 50.402ms
2025-08-19 11:50:30.927 | INFO     | c715f4017b064540b2dd9d605b390a85 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 54.963ms
2025-08-19 11:50:33.667 | INFO     | e1f11d42020d497380b466be9433892b | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 11:50:33.670 | INFO     | e1f11d42020d497380b466be9433892b | 成功认证Java用户: pythontest
2025-08-19 11:50:33.680 | INFO     | e1f11d42020d497380b466be9433892b | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 11:50:33.681 | INFO     | e1f11d42020d497380b466be9433892b | 权限检查通过: user_id=42, permission=knowledge:base:delete
2025-08-19 11:50:33.836 | INFO     | e1f11d42020d497380b466be9433892b | HTTP Request: DELETE http://*************:6610/api/v1/datasets "HTTP/1.1 200 OK"
2025-08-19 11:50:33.837 | INFO     | e1f11d42020d497380b466be9433892b | 127.0.0.1       | DELETE   | 200    | /api/iot/v1/knowledge-base/delete | 170.222ms
2025-08-19 11:50:33.845 | INFO     | f7812fc1ae5c498dbb9b98ea95c668b7 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 11:50:33.845 | INFO     | 161301faad4b46ecb6fe48fec24b32c2 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 11:50:33.847 | INFO     | f7812fc1ae5c498dbb9b98ea95c668b7 | 成功认证Java用户: pythontest
2025-08-19 11:50:33.849 | INFO     | 161301faad4b46ecb6fe48fec24b32c2 | 成功认证Java用户: pythontest
2025-08-19 11:50:33.856 | INFO     | 161301faad4b46ecb6fe48fec24b32c2 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 11:50:33.857 | INFO     | 161301faad4b46ecb6fe48fec24b32c2 | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-19 11:50:33.863 | INFO     | f7812fc1ae5c498dbb9b98ea95c668b7 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 11:50:33.863 | INFO     | f7812fc1ae5c498dbb9b98ea95c668b7 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 11:50:33.889 | INFO     | 161301faad4b46ecb6fe48fec24b32c2 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 11:50:33.891 | INFO     | f7812fc1ae5c498dbb9b98ea95c668b7 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 11:50:33.892 | INFO     | 161301faad4b46ecb6fe48fec24b32c2 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 46.918ms
2025-08-19 11:50:33.895 | INFO     | f7812fc1ae5c498dbb9b98ea95c668b7 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 51.112ms
2025-08-19 11:52:48.783 | INFO     | - | ✅ Java系统数据库连接创建成功
2025-08-19 11:55:03.278 | INFO     | c50633c8f69f426ba779318d1999aca5 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-19 11:55:03.279 | INFO     | c50633c8f69f426ba779318d1999aca5 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 23.614ms
2025-08-19 11:55:03.674 | INFO     | d1ded92fb43b4e7b9c3a134275fad3e9 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 11:55:03.676 | INFO     | d1ded92fb43b4e7b9c3a134275fad3e9 | 成功认证Java用户: pythontest
2025-08-19 11:55:03.689 | INFO     | d1ded92fb43b4e7b9c3a134275fad3e9 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 11:55:03.689 | INFO     | d1ded92fb43b4e7b9c3a134275fad3e9 | 权限检查通过: user_id=42, permission=knowledge:base:create
2025-08-19 11:55:03.689 | INFO     | d1ded92fb43b4e7b9c3a134275fad3e9 | 创建知识库请求数据: {'name': 'test_updated_api_536955', 'description': '测试重启后的接口', 'permission': 'me', 'chunk_method': 'naive', 'embedding_model': 'bge-m3:latest@Ollama'}
2025-08-19 11:55:03.703 | INFO     | d1ded92fb43b4e7b9c3a134275fad3e9 | HTTP Request: POST http://*************:6610/api/v1/datasets "HTTP/1.1 200 OK"
2025-08-19 11:55:03.704 | INFO     | d1ded92fb43b4e7b9c3a134275fad3e9 | 尝试使用备用模型: BAAI/bge-zh-v1.5
2025-08-19 11:55:03.713 | INFO     | d1ded92fb43b4e7b9c3a134275fad3e9 | HTTP Request: POST http://*************:6610/api/v1/datasets "HTTP/1.1 200 OK"
2025-08-19 11:55:03.714 | INFO     | d1ded92fb43b4e7b9c3a134275fad3e9 | 尝试使用备用模型: BAAI/bge-large-zh-v1.5
2025-08-19 11:55:03.722 | INFO     | d1ded92fb43b4e7b9c3a134275fad3e9 | HTTP Request: POST http://*************:6610/api/v1/datasets "HTTP/1.1 200 OK"
2025-08-19 11:55:03.723 | INFO     | d1ded92fb43b4e7b9c3a134275fad3e9 | 尝试使用备用模型: text-embedding-ada-002
2025-08-19 11:55:03.732 | INFO     | d1ded92fb43b4e7b9c3a134275fad3e9 | HTTP Request: POST http://*************:6610/api/v1/datasets "HTTP/1.1 200 OK"
2025-08-19 11:55:03.733 | INFO     | d1ded92fb43b4e7b9c3a134275fad3e9 | 127.0.0.1       | POST     | 200    | /api/iot/v1/knowledge-base | 62.769ms
2025-08-19 11:55:47.003 | INFO     | - | ✅ Java系统数据库连接创建成功
2025-08-19 11:56:16.227 | INFO     | 4c7a61056b8f40f18fd4aa9299c1af33 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-19 11:56:16.229 | INFO     | 4c7a61056b8f40f18fd4aa9299c1af33 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 22.588ms
2025-08-19 11:56:16.619 | INFO     | 8a3741e075e1418b8765e63dac83965b | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 11:56:16.620 | INFO     | 8a3741e075e1418b8765e63dac83965b | 成功认证Java用户: pythontest
2025-08-19 11:56:16.634 | INFO     | 8a3741e075e1418b8765e63dac83965b | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 11:56:16.634 | INFO     | 8a3741e075e1418b8765e63dac83965b | 权限检查通过: user_id=42, permission=knowledge:base:create
2025-08-19 11:56:16.634 | INFO     | 8a3741e075e1418b8765e63dac83965b | 创建知识库请求数据: {'name': 'test_updated_api_537028', 'description': '测试重启后的接口', 'permission': 'me', 'chunk_method': 'naive'}
2025-08-19 11:56:16.955 | INFO     | 8a3741e075e1418b8765e63dac83965b | HTTP Request: POST http://*************:6610/api/v1/datasets "HTTP/1.1 200 OK"
2025-08-19 11:56:16.956 | INFO     | 8a3741e075e1418b8765e63dac83965b | 127.0.0.1       | POST     | 200    | /api/iot/v1/knowledge-base | 340.656ms
2025-08-19 11:57:19.477 | INFO     | 2fe60a141a404ac4ae1fc662f6a1c0a5 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 11:57:19.480 | INFO     | 2fe60a141a404ac4ae1fc662f6a1c0a5 | 成功认证Java用户: pythontest
2025-08-19 11:57:19.499 | INFO     | 2fe60a141a404ac4ae1fc662f6a1c0a5 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 11:57:19.500 | INFO     | 2fe60a141a404ac4ae1fc662f6a1c0a5 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 11:57:19.516 | INFO     | 2fe60a141a404ac4ae1fc662f6a1c0a5 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 11:57:19.517 | INFO     | 2fe60a141a404ac4ae1fc662f6a1c0a5 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 40.129ms
2025-08-19 11:57:25.617 | INFO     | 8bca2bc570f048bca68efc453cdf9757 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 11:57:25.619 | INFO     | 8bca2bc570f048bca68efc453cdf9757 | 成功认证Java用户: pythontest
2025-08-19 11:57:25.630 | INFO     | 8bca2bc570f048bca68efc453cdf9757 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 11:57:25.631 | INFO     | 8bca2bc570f048bca68efc453cdf9757 | 权限检查通过: user_id=42, permission=knowledge:base:create
2025-08-19 11:57:25.632 | INFO     | 8bca2bc570f048bca68efc453cdf9757 | 创建知识库请求数据: {'name': 'pythontest2', 'description': '', 'embedding_model': 'bge-m3:latest@Ollama', 'permission': 'me', 'chunk_method': 'naive', 'parser_config': {'chunk_token_num': 128, 'delimiter': '\\n!?;。；！？', 'html4excel': False, 'layout_recognize': 'DeepDOC', 'auto_keywords': 0, 'auto_questions': 0, 'task_page_size': 12}}
2025-08-19 11:57:25.787 | INFO     | 8bca2bc570f048bca68efc453cdf9757 | HTTP Request: POST http://*************:6610/api/v1/datasets "HTTP/1.1 200 OK"
2025-08-19 11:57:25.789 | INFO     | 8bca2bc570f048bca68efc453cdf9757 | 尝试使用备用模型: BAAI/bge-zh-v1.5
2025-08-19 11:57:25.800 | INFO     | 8bca2bc570f048bca68efc453cdf9757 | HTTP Request: POST http://*************:6610/api/v1/datasets "HTTP/1.1 200 OK"
2025-08-19 11:57:25.801 | INFO     | 8bca2bc570f048bca68efc453cdf9757 | 尝试使用备用模型: BAAI/bge-large-zh-v1.5
2025-08-19 11:57:25.811 | INFO     | 8bca2bc570f048bca68efc453cdf9757 | HTTP Request: POST http://*************:6610/api/v1/datasets "HTTP/1.1 200 OK"
2025-08-19 11:57:25.813 | INFO     | 8bca2bc570f048bca68efc453cdf9757 | 尝试使用备用模型: text-embedding-ada-002
2025-08-19 11:57:25.823 | INFO     | 8bca2bc570f048bca68efc453cdf9757 | HTTP Request: POST http://*************:6610/api/v1/datasets "HTTP/1.1 200 OK"
2025-08-19 11:57:25.825 | INFO     | 8bca2bc570f048bca68efc453cdf9757 | 127.0.0.1       | POST     | 200    | /api/iot/v1/knowledge-base | 208.528ms
2025-08-19 11:58:33.759 | INFO     | a6fad0f8a97a4fdba16b523e6492a73e | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 11:58:33.760 | INFO     | 8e950f2cc27b4b4eacfa902f9a119ccf | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 11:58:33.761 | INFO     | 0d378a34e83842f081cfe9134b3cc6f6 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 11:58:33.762 | INFO     | a6fad0f8a97a4fdba16b523e6492a73e | 成功认证Java用户: pythontest
2025-08-19 11:58:33.764 | INFO     | 8e950f2cc27b4b4eacfa902f9a119ccf | 成功认证Java用户: pythontest
2025-08-19 11:58:33.765 | INFO     | 0d378a34e83842f081cfe9134b3cc6f6 | 成功认证Java用户: pythontest
2025-08-19 11:58:33.773 | INFO     | 0d378a34e83842f081cfe9134b3cc6f6 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 11:58:33.773 | INFO     | 0d378a34e83842f081cfe9134b3cc6f6 | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-19 11:58:33.778 | INFO     | 8e950f2cc27b4b4eacfa902f9a119ccf | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 11:58:33.779 | INFO     | 8e950f2cc27b4b4eacfa902f9a119ccf | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 11:58:33.783 | INFO     | a6fad0f8a97a4fdba16b523e6492a73e | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-19 11:58:33.784 | INFO     | a6fad0f8a97a4fdba16b523e6492a73e | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 24.962ms
2025-08-19 11:58:33.788 | INFO     | 0d378a34e83842f081cfe9134b3cc6f6 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 11:58:33.789 | INFO     | 0d378a34e83842f081cfe9134b3cc6f6 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 29.405ms
2025-08-19 11:58:33.793 | INFO     | 8e950f2cc27b4b4eacfa902f9a119ccf | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 11:58:33.794 | INFO     | 8e950f2cc27b4b4eacfa902f9a119ccf | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 33.430ms
2025-08-19 11:58:54.025 | INFO     | 898fa2e4f2b34685841f7ed95cc4ca07 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 11:58:54.027 | INFO     | 898fa2e4f2b34685841f7ed95cc4ca07 | 成功认证Java用户: pythontest
2025-08-19 11:58:54.040 | INFO     | 898fa2e4f2b34685841f7ed95cc4ca07 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-19 11:58:54.041 | INFO     | 898fa2e4f2b34685841f7ed95cc4ca07 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 15.791ms
2025-08-19 11:58:54.043 | INFO     | 6fef77cf952e4fa485936735632d2547 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 11:58:54.044 | INFO     | 6fef77cf952e4fa485936735632d2547 | 成功认证Java用户: pythontest
2025-08-19 11:58:54.049 | INFO     | 6fef77cf952e4fa485936735632d2547 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 11:58:54.050 | INFO     | 6fef77cf952e4fa485936735632d2547 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 11:58:54.063 | INFO     | 6fef77cf952e4fa485936735632d2547 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 11:58:54.064 | INFO     | 6fef77cf952e4fa485936735632d2547 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 21.185ms
2025-08-19 11:58:54.066 | INFO     | 0f70ce47078042b9b83f71bd66dbdd37 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 11:58:54.068 | INFO     | 0f70ce47078042b9b83f71bd66dbdd37 | 成功认证Java用户: pythontest
2025-08-19 11:58:54.073 | INFO     | 0f70ce47078042b9b83f71bd66dbdd37 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 11:58:54.073 | INFO     | 0f70ce47078042b9b83f71bd66dbdd37 | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-19 11:58:54.087 | INFO     | 0f70ce47078042b9b83f71bd66dbdd37 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 11:58:54.088 | INFO     | 0f70ce47078042b9b83f71bd66dbdd37 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 21.319ms
2025-08-19 13:31:43.685 | INFO     | aa0d8ae6481c4cd89e1256217f768a36 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 13:31:43.751 | INFO     | aa0d8ae6481c4cd89e1256217f768a36 | 成功认证Java用户: pythontest
2025-08-19 13:31:44.117 | INFO     | aa0d8ae6481c4cd89e1256217f768a36 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 13:31:44.118 | INFO     | aa0d8ae6481c4cd89e1256217f768a36 | 权限检查通过: user_id=42, permission=knowledge:base:create
2025-08-19 13:31:44.118 | INFO     | aa0d8ae6481c4cd89e1256217f768a36 | 创建知识库请求数据: {'name': 'frontend_fixed_test_542755', 'description': '测试前端修复后的接口', 'permission': 'me', 'chunk_method': 'naive', 'parser_config': {'chunk_token_num': 128, 'delimiter': '\\n!?;。；！？', 'html4excel': False, 'layout_recognize': 'DeepDOC', 'auto_keywords': 0, 'auto_questions': 0, 'task_page_size': 12}}
2025-08-19 13:31:44.551 | INFO     | aa0d8ae6481c4cd89e1256217f768a36 | HTTP Request: POST http://*************:6610/api/v1/datasets "HTTP/1.1 200 OK"
2025-08-19 13:31:44.552 | INFO     | aa0d8ae6481c4cd89e1256217f768a36 | 127.0.0.1       | POST     | 200    | /api/iot/v1/knowledge-base | 867.315ms
2025-08-19 13:31:44.950 | INFO     | ******************************** | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 13:31:44.950 | INFO     | ******************************** | 成功认证Java用户: pythontest
2025-08-19 13:31:44.967 | INFO     | ******************************** | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 13:31:44.967 | INFO     | ******************************** | 权限检查通过: user_id=42, permission=knowledge:base:create
2025-08-19 13:31:44.968 | INFO     | ******************************** | 创建知识库请求数据: {'name': 'explicit_model_test_542756', 'description': '测试明确指定嵌入模型', 'embedding_model': 'text-embedding-bge-m3@LM-Studio', 'permission': 'me', 'chunk_method': 'naive'}
2025-08-19 13:31:44.984 | INFO     | ******************************** | HTTP Request: POST http://*************:6610/api/v1/datasets "HTTP/1.1 200 OK"
2025-08-19 13:31:44.985 | INFO     | ******************************** | 尝试使用备用模型: BAAI/bge-zh-v1.5
2025-08-19 13:31:44.996 | INFO     | ******************************** | HTTP Request: POST http://*************:6610/api/v1/datasets "HTTP/1.1 200 OK"
2025-08-19 13:31:44.997 | INFO     | ******************************** | 尝试使用备用模型: BAAI/bge-large-zh-v1.5
2025-08-19 13:31:45.008 | INFO     | ******************************** | HTTP Request: POST http://*************:6610/api/v1/datasets "HTTP/1.1 200 OK"
2025-08-19 13:31:45.009 | INFO     | ******************************** | 尝试使用备用模型: text-embedding-ada-002
2025-08-19 13:31:45.021 | INFO     | ******************************** | HTTP Request: POST http://*************:6610/api/v1/datasets "HTTP/1.1 200 OK"
2025-08-19 13:31:45.022 | INFO     | ******************************** | 127.0.0.1       | POST     | 200    | /api/iot/v1/knowledge-base | 72.546ms
2025-08-19 13:32:58.649 | INFO     | 0e25f598a54048b0ae72c319dbebae17 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 13:32:58.660 | INFO     | 0e25f598a54048b0ae72c319dbebae17 | 成功认证Java用户: pythontest
2025-08-19 13:32:58.675 | INFO     | 0e25f598a54048b0ae72c319dbebae17 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 13:32:58.676 | INFO     | 0e25f598a54048b0ae72c319dbebae17 | 权限检查通过: user_id=42, permission=knowledge:base:create
2025-08-19 13:32:58.676 | INFO     | 0e25f598a54048b0ae72c319dbebae17 | 创建知识库请求数据: {'name': 'pythontest2', 'description': '', 'embedding_model': 'bge-m3:latest@Ollama', 'permission': 'me', 'chunk_method': 'naive', 'parser_config': {'chunk_token_num': 128, 'delimiter': '\\n!?;。；！？', 'html4excel': False, 'layout_recognize': 'DeepDOC', 'auto_keywords': 0, 'auto_questions': 0, 'task_page_size': 12}}
2025-08-19 13:32:58.692 | INFO     | 0e25f598a54048b0ae72c319dbebae17 | HTTP Request: POST http://*************:6610/api/v1/datasets "HTTP/1.1 200 OK"
2025-08-19 13:32:58.693 | INFO     | 0e25f598a54048b0ae72c319dbebae17 | 尝试使用备用模型: BAAI/bge-zh-v1.5
2025-08-19 13:32:58.703 | INFO     | 0e25f598a54048b0ae72c319dbebae17 | HTTP Request: POST http://*************:6610/api/v1/datasets "HTTP/1.1 200 OK"
2025-08-19 13:32:58.704 | INFO     | 0e25f598a54048b0ae72c319dbebae17 | 尝试使用备用模型: BAAI/bge-large-zh-v1.5
2025-08-19 13:32:58.714 | INFO     | 0e25f598a54048b0ae72c319dbebae17 | HTTP Request: POST http://*************:6610/api/v1/datasets "HTTP/1.1 200 OK"
2025-08-19 13:32:58.715 | INFO     | 0e25f598a54048b0ae72c319dbebae17 | 尝试使用备用模型: text-embedding-ada-002
2025-08-19 13:32:58.726 | INFO     | 0e25f598a54048b0ae72c319dbebae17 | HTTP Request: POST http://*************:6610/api/v1/datasets "HTTP/1.1 200 OK"
2025-08-19 13:32:58.727 | INFO     | 0e25f598a54048b0ae72c319dbebae17 | 127.0.0.1       | POST     | 200    | /api/iot/v1/knowledge-base | 77.288ms
2025-08-19 13:33:15.600 | INFO     | - | ✅ Java系统数据库连接创建成功
2025-08-19 13:33:20.927 | INFO     | dfc902a2fa474926a595eed538191643 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 13:33:20.929 | INFO     | 464b59908a874129aac539e211366bfd | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 13:33:20.932 | INFO     | dfc902a2fa474926a595eed538191643 | 成功认证Java用户: pythontest
2025-08-19 13:33:20.935 | INFO     | 464b59908a874129aac539e211366bfd | 成功认证Java用户: pythontest
2025-08-19 13:33:20.972 | INFO     | dfc902a2fa474926a595eed538191643 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-19 13:33:20.975 | INFO     | dfc902a2fa474926a595eed538191643 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 56.587ms
2025-08-19 13:33:20.977 | INFO     | 464b59908a874129aac539e211366bfd | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 13:33:20.977 | INFO     | 464b59908a874129aac539e211366bfd | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 13:33:20.982 | INFO     | d3391c225dd545908bce5c5ed7ceaec2 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 13:33:20.985 | INFO     | d3391c225dd545908bce5c5ed7ceaec2 | 成功认证Java用户: pythontest
2025-08-19 13:33:21.004 | INFO     | 464b59908a874129aac539e211366bfd | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 13:33:21.006 | INFO     | 464b59908a874129aac539e211366bfd | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 76.729ms
2025-08-19 13:33:21.011 | INFO     | d3391c225dd545908bce5c5ed7ceaec2 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 13:33:21.011 | INFO     | d3391c225dd545908bce5c5ed7ceaec2 | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-19 13:33:21.027 | INFO     | d3391c225dd545908bce5c5ed7ceaec2 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 13:33:21.029 | INFO     | d3391c225dd545908bce5c5ed7ceaec2 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 47.833ms
2025-08-19 13:33:22.440 | INFO     | d991147887924ef5bc2c48f652fe6a86 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 13:33:22.441 | INFO     | fdd872a105e24437beb9d6722af4cdf4 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 13:33:22.442 | INFO     | d991147887924ef5bc2c48f652fe6a86 | 成功认证Java用户: pythontest
2025-08-19 13:33:22.443 | INFO     | 1e2a90f1cc9748059fe3ec981a6c64fd | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 13:33:22.444 | INFO     | fdd872a105e24437beb9d6722af4cdf4 | 成功认证Java用户: pythontest
2025-08-19 13:33:22.448 | INFO     | 1e2a90f1cc9748059fe3ec981a6c64fd | 成功认证Java用户: pythontest
2025-08-19 13:33:22.463 | INFO     | 1e2a90f1cc9748059fe3ec981a6c64fd | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 13:33:22.464 | INFO     | 1e2a90f1cc9748059fe3ec981a6c64fd | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-19 13:33:22.471 | INFO     | d991147887924ef5bc2c48f652fe6a86 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-19 13:33:22.478 | INFO     | d991147887924ef5bc2c48f652fe6a86 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 38.374ms
2025-08-19 13:33:22.486 | INFO     | fdd872a105e24437beb9d6722af4cdf4 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 13:33:22.487 | INFO     | fdd872a105e24437beb9d6722af4cdf4 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 13:33:22.492 | INFO     | 1e2a90f1cc9748059fe3ec981a6c64fd | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 13:33:22.498 | INFO     | 1e2a90f1cc9748059fe3ec981a6c64fd | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 55.987ms
2025-08-19 13:33:22.511 | INFO     | fdd872a105e24437beb9d6722af4cdf4 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 13:33:22.513 | INFO     | fdd872a105e24437beb9d6722af4cdf4 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 72.445ms
2025-08-19 13:33:34.259 | INFO     | 90d57a86a38b41ed8f0492cdf3c42020 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 13:33:34.260 | INFO     | 90d57a86a38b41ed8f0492cdf3c42020 | 成功认证Java用户: pythontest
2025-08-19 13:33:34.273 | INFO     | 90d57a86a38b41ed8f0492cdf3c42020 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 13:33:34.273 | INFO     | 90d57a86a38b41ed8f0492cdf3c42020 | 权限检查通过: user_id=42, permission=knowledge:base:create
2025-08-19 13:33:34.274 | INFO     | 90d57a86a38b41ed8f0492cdf3c42020 | 创建知识库请求数据: {'name': 'pythontest2', 'description': '', 'embedding_model': 'bge-m3:latest@Ollama', 'permission': 'me', 'chunk_method': 'naive', 'parser_config': {'chunk_token_num': 128, 'delimiter': '\\n!?;。；！？', 'html4excel': False, 'layout_recognize': 'DeepDOC', 'auto_keywords': 0, 'auto_questions': 0, 'task_page_size': 12}}
2025-08-19 13:33:34.291 | INFO     | 90d57a86a38b41ed8f0492cdf3c42020 | HTTP Request: POST http://*************:6610/api/v1/datasets "HTTP/1.1 200 OK"
2025-08-19 13:33:34.293 | INFO     | 90d57a86a38b41ed8f0492cdf3c42020 | 尝试使用备用模型: BAAI/bge-zh-v1.5
2025-08-19 13:33:34.303 | INFO     | 90d57a86a38b41ed8f0492cdf3c42020 | HTTP Request: POST http://*************:6610/api/v1/datasets "HTTP/1.1 200 OK"
2025-08-19 13:33:34.305 | INFO     | 90d57a86a38b41ed8f0492cdf3c42020 | 尝试使用备用模型: BAAI/bge-large-zh-v1.5
2025-08-19 13:33:34.319 | INFO     | 90d57a86a38b41ed8f0492cdf3c42020 | HTTP Request: POST http://*************:6610/api/v1/datasets "HTTP/1.1 200 OK"
2025-08-19 13:33:34.320 | INFO     | 90d57a86a38b41ed8f0492cdf3c42020 | 尝试使用备用模型: text-embedding-ada-002
2025-08-19 13:33:34.333 | INFO     | 90d57a86a38b41ed8f0492cdf3c42020 | HTTP Request: POST http://*************:6610/api/v1/datasets "HTTP/1.1 200 OK"
2025-08-19 13:33:34.334 | INFO     | 90d57a86a38b41ed8f0492cdf3c42020 | 127.0.0.1       | POST     | 200    | /api/iot/v1/knowledge-base | 75.933ms
2025-08-19 13:34:23.224 | INFO     | 5706e527c70d4fc788277cb0fcf054e8 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 13:34:23.226 | INFO     | 771c66f6d19549e5ae85f45bcc50a3ad | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 13:34:23.226 | INFO     | cc50d91095254f23abddb85f767ad6e1 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 13:34:23.228 | INFO     | 5706e527c70d4fc788277cb0fcf054e8 | 成功认证Java用户: pythontest
2025-08-19 13:34:23.230 | INFO     | 771c66f6d19549e5ae85f45bcc50a3ad | 成功认证Java用户: pythontest
2025-08-19 13:34:23.232 | INFO     | cc50d91095254f23abddb85f767ad6e1 | 成功认证Java用户: pythontest
2025-08-19 13:34:23.244 | INFO     | cc50d91095254f23abddb85f767ad6e1 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 13:34:23.245 | INFO     | cc50d91095254f23abddb85f767ad6e1 | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-19 13:34:23.251 | INFO     | 771c66f6d19549e5ae85f45bcc50a3ad | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 13:34:23.252 | INFO     | 771c66f6d19549e5ae85f45bcc50a3ad | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 13:34:23.257 | INFO     | 5706e527c70d4fc788277cb0fcf054e8 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-19 13:34:23.263 | INFO     | 5706e527c70d4fc788277cb0fcf054e8 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 39.659ms
2025-08-19 13:34:23.279 | INFO     | cc50d91095254f23abddb85f767ad6e1 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 13:34:23.281 | INFO     | 771c66f6d19549e5ae85f45bcc50a3ad | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 13:34:23.282 | INFO     | cc50d91095254f23abddb85f767ad6e1 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 57.603ms
2025-08-19 13:34:23.285 | INFO     | 771c66f6d19549e5ae85f45bcc50a3ad | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 60.083ms
2025-08-19 13:34:50.674 | INFO     | 80d44c8096d7422fa80537ea156d5326 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 13:34:50.675 | INFO     | 80d44c8096d7422fa80537ea156d5326 | 成功认证Java用户: pythontest
2025-08-19 13:34:50.683 | INFO     | 80d44c8096d7422fa80537ea156d5326 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 13:34:50.685 | INFO     | 80d44c8096d7422fa80537ea156d5326 | 权限检查通过: user_id=42, permission=knowledge:base:create
2025-08-19 13:34:50.685 | INFO     | 80d44c8096d7422fa80537ea156d5326 | 创建知识库请求数据: {'name': 'pythontest2', 'description': '', 'permission': 'me', 'chunk_method': 'naive', 'parser_config': {'chunk_token_num': 128, 'delimiter': '\\n!?;。；！？', 'html4excel': False, 'layout_recognize': 'DeepDOC', 'auto_keywords': 0, 'auto_questions': 0, 'task_page_size': 12}}
2025-08-19 13:34:50.873 | INFO     | 80d44c8096d7422fa80537ea156d5326 | HTTP Request: POST http://*************:6610/api/v1/datasets "HTTP/1.1 200 OK"
2025-08-19 13:34:50.874 | INFO     | 80d44c8096d7422fa80537ea156d5326 | 127.0.0.1       | POST     | 200    | /api/iot/v1/knowledge-base | 200.936ms
2025-08-19 13:34:50.888 | INFO     | fb0ff833b7554c3498aa31028f112d77 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 13:34:50.891 | INFO     | fb0ff833b7554c3498aa31028f112d77 | 成功认证Java用户: pythontest
2025-08-19 13:34:50.900 | INFO     | fb0ff833b7554c3498aa31028f112d77 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 13:34:50.901 | INFO     | fb0ff833b7554c3498aa31028f112d77 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 13:34:50.917 | INFO     | fb0ff833b7554c3498aa31028f112d77 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 13:34:50.919 | INFO     | fb0ff833b7554c3498aa31028f112d77 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 30.754ms
2025-08-19 13:34:50.923 | INFO     | 4f7ab12c8d9b4d1e97b5f672e517a755 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 13:34:50.924 | INFO     | 4f7ab12c8d9b4d1e97b5f672e517a755 | 成功认证Java用户: pythontest
2025-08-19 13:34:50.932 | INFO     | 4f7ab12c8d9b4d1e97b5f672e517a755 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 13:34:50.933 | INFO     | 4f7ab12c8d9b4d1e97b5f672e517a755 | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-19 13:34:50.951 | INFO     | 4f7ab12c8d9b4d1e97b5f672e517a755 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 13:34:50.953 | INFO     | 4f7ab12c8d9b4d1e97b5f672e517a755 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 31.037ms
2025-08-19 13:34:53.279 | INFO     | 75a9b9d29cd44d14ba466f6ecd534d00 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 13:34:53.281 | INFO     | 75a9b9d29cd44d14ba466f6ecd534d00 | 成功认证Java用户: pythontest
2025-08-19 13:34:53.288 | INFO     | 75a9b9d29cd44d14ba466f6ecd534d00 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 13:34:53.288 | INFO     | 75a9b9d29cd44d14ba466f6ecd534d00 | 权限检查通过: user_id=42, permission=knowledge:base:view
2025-08-19 13:34:53.386 | INFO     | 75a9b9d29cd44d14ba466f6ecd534d00 | HTTP Request: GET http://*************:6610/api/v1/datasets/3a54e7487cbe11f0a8f6ea5dc8d5776c "HTTP/1.1 200 OK"
2025-08-19 13:34:53.388 | INFO     | 75a9b9d29cd44d14ba466f6ecd534d00 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/3a54e7487cbe11f0a8f6ea5dc8d5776c | 108.836ms
2025-08-19 13:34:54.357 | INFO     | 88247eabc6f246268fcec1dc25b5ecfc | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 13:34:54.359 | INFO     | 88247eabc6f246268fcec1dc25b5ecfc | 成功认证Java用户: pythontest
2025-08-19 13:34:54.366 | INFO     | 88247eabc6f246268fcec1dc25b5ecfc | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 13:34:54.367 | INFO     | 88247eabc6f246268fcec1dc25b5ecfc | 权限检查通过: user_id=42, permission=knowledge:base:view
2025-08-19 13:34:54.378 | INFO     | 88247eabc6f246268fcec1dc25b5ecfc | HTTP Request: GET http://*************:6610/api/v1/datasets/3a54e7487cbe11f0a8f6ea5dc8d5776c "HTTP/1.1 200 OK"
2025-08-19 13:34:54.380 | INFO     | 88247eabc6f246268fcec1dc25b5ecfc | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/3a54e7487cbe11f0a8f6ea5dc8d5776c | 23.252ms
2025-08-19 13:34:54.826 | INFO     | afc1fe9a679c4f5b95073a807481c0cf | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 13:34:54.829 | INFO     | afc1fe9a679c4f5b95073a807481c0cf | 成功认证Java用户: pythontest
2025-08-19 13:34:54.836 | INFO     | afc1fe9a679c4f5b95073a807481c0cf | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 13:34:54.836 | INFO     | afc1fe9a679c4f5b95073a807481c0cf | 权限检查通过: user_id=42, permission=knowledge:base:view
2025-08-19 13:34:54.848 | INFO     | afc1fe9a679c4f5b95073a807481c0cf | HTTP Request: GET http://*************:6610/api/v1/datasets/3a54e7487cbe11f0a8f6ea5dc8d5776c "HTTP/1.1 200 OK"
2025-08-19 13:34:54.850 | INFO     | afc1fe9a679c4f5b95073a807481c0cf | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/3a54e7487cbe11f0a8f6ea5dc8d5776c | 23.707ms
2025-08-19 13:34:55.105 | INFO     | bb23a05a79e44477a8892be90c5ed3b9 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 13:34:55.106 | INFO     | bb23a05a79e44477a8892be90c5ed3b9 | 成功认证Java用户: pythontest
2025-08-19 13:34:55.114 | INFO     | bb23a05a79e44477a8892be90c5ed3b9 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 13:34:55.115 | INFO     | bb23a05a79e44477a8892be90c5ed3b9 | 权限检查通过: user_id=42, permission=knowledge:base:view
2025-08-19 13:34:55.128 | INFO     | bb23a05a79e44477a8892be90c5ed3b9 | HTTP Request: GET http://*************:6610/api/v1/datasets/3a54e7487cbe11f0a8f6ea5dc8d5776c "HTTP/1.1 200 OK"
2025-08-19 13:34:55.131 | INFO     | bb23a05a79e44477a8892be90c5ed3b9 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/3a54e7487cbe11f0a8f6ea5dc8d5776c | 26.744ms
2025-08-19 13:34:55.477 | INFO     | 6da2178367094455824c7fa9aeb4958d | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 13:34:55.479 | INFO     | 6da2178367094455824c7fa9aeb4958d | 成功认证Java用户: pythontest
2025-08-19 13:34:55.485 | INFO     | 6da2178367094455824c7fa9aeb4958d | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 13:34:55.485 | INFO     | 6da2178367094455824c7fa9aeb4958d | 权限检查通过: user_id=42, permission=knowledge:base:view
2025-08-19 13:34:55.498 | INFO     | 6da2178367094455824c7fa9aeb4958d | HTTP Request: GET http://*************:6610/api/v1/datasets/3a54e7487cbe11f0a8f6ea5dc8d5776c "HTTP/1.1 200 OK"
2025-08-19 13:34:55.500 | INFO     | 6da2178367094455824c7fa9aeb4958d | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/3a54e7487cbe11f0a8f6ea5dc8d5776c | 22.536ms
2025-08-19 13:35:08.406 | INFO     | ff8917aaa72b4c2c84f3ba35a2eb18ea | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 13:35:08.407 | INFO     | ff8917aaa72b4c2c84f3ba35a2eb18ea | 成功认证Java用户: pythontest
2025-08-19 13:35:08.416 | INFO     | ff8917aaa72b4c2c84f3ba35a2eb18ea | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 13:35:08.418 | INFO     | ff8917aaa72b4c2c84f3ba35a2eb18ea | 权限检查通过: user_id=42, permission=knowledge:base:delete
2025-08-19 13:35:08.563 | INFO     | ff8917aaa72b4c2c84f3ba35a2eb18ea | HTTP Request: DELETE http://*************:6610/api/v1/datasets "HTTP/1.1 200 OK"
2025-08-19 13:35:08.564 | INFO     | ff8917aaa72b4c2c84f3ba35a2eb18ea | 127.0.0.1       | DELETE   | 200    | /api/iot/v1/knowledge-base/delete | 157.620ms
2025-08-19 13:35:08.575 | INFO     | 97f839e3e1604c1abb8d3cd2a019aa8f | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 13:35:08.578 | INFO     | 97f839e3e1604c1abb8d3cd2a019aa8f | 成功认证Java用户: pythontest
2025-08-19 13:35:08.586 | INFO     | 97f839e3e1604c1abb8d3cd2a019aa8f | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 13:35:08.588 | INFO     | 97f839e3e1604c1abb8d3cd2a019aa8f | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 13:35:08.603 | INFO     | 97f839e3e1604c1abb8d3cd2a019aa8f | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 13:35:08.605 | INFO     | 97f839e3e1604c1abb8d3cd2a019aa8f | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 30.723ms
2025-08-19 13:35:08.609 | INFO     | 39a118930eb14351954936360ebba3e4 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 13:35:08.610 | INFO     | 39a118930eb14351954936360ebba3e4 | 成功认证Java用户: pythontest
2025-08-19 13:35:08.617 | INFO     | 39a118930eb14351954936360ebba3e4 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 13:35:08.618 | INFO     | 39a118930eb14351954936360ebba3e4 | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-19 13:35:08.637 | INFO     | 39a118930eb14351954936360ebba3e4 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 13:35:08.639 | INFO     | 39a118930eb14351954936360ebba3e4 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 30.618ms
2025-08-19 13:35:10.786 | INFO     | 8b3438fb1dfb47f885c99d1ef971ac2e | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 13:35:10.787 | INFO     | 8b3438fb1dfb47f885c99d1ef971ac2e | 成功认证Java用户: pythontest
2025-08-19 13:35:10.795 | INFO     | 8b3438fb1dfb47f885c99d1ef971ac2e | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 13:35:10.796 | INFO     | 8b3438fb1dfb47f885c99d1ef971ac2e | 权限检查通过: user_id=42, permission=knowledge:base:delete
2025-08-19 13:35:11.121 | INFO     | 8b3438fb1dfb47f885c99d1ef971ac2e | HTTP Request: DELETE http://*************:6610/api/v1/datasets "HTTP/1.1 200 OK"
2025-08-19 13:35:11.123 | INFO     | 8b3438fb1dfb47f885c99d1ef971ac2e | 127.0.0.1       | DELETE   | 200    | /api/iot/v1/knowledge-base/delete | 337.540ms
2025-08-19 13:35:11.135 | INFO     | e33f36231adb4b8897a21d159a792440 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 13:35:11.137 | INFO     | 0e1a089ca6254fee81de5eff254619af | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 13:35:11.138 | INFO     | e33f36231adb4b8897a21d159a792440 | 成功认证Java用户: pythontest
2025-08-19 13:35:11.141 | INFO     | 0e1a089ca6254fee81de5eff254619af | 成功认证Java用户: pythontest
2025-08-19 13:35:11.149 | INFO     | e33f36231adb4b8897a21d159a792440 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 13:35:11.150 | INFO     | e33f36231adb4b8897a21d159a792440 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 13:35:11.155 | INFO     | 0e1a089ca6254fee81de5eff254619af | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 13:35:11.156 | INFO     | 0e1a089ca6254fee81de5eff254619af | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-19 13:35:11.200 | INFO     | 0e1a089ca6254fee81de5eff254619af | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 13:35:11.201 | INFO     | e33f36231adb4b8897a21d159a792440 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 13:35:11.204 | INFO     | 0e1a089ca6254fee81de5eff254619af | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 67.908ms
2025-08-19 13:35:11.205 | INFO     | e33f36231adb4b8897a21d159a792440 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 70.552ms
2025-08-19 13:35:15.805 | INFO     | 91664892a78c4344ba421d3885e35cc6 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 13:35:15.806 | INFO     | 91664892a78c4344ba421d3885e35cc6 | 成功认证Java用户: pythontest
2025-08-19 13:35:15.813 | INFO     | 91664892a78c4344ba421d3885e35cc6 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 13:35:15.814 | INFO     | 91664892a78c4344ba421d3885e35cc6 | 权限检查通过: user_id=42, permission=knowledge:base:view
2025-08-19 13:35:15.825 | INFO     | 91664892a78c4344ba421d3885e35cc6 | HTTP Request: GET http://*************:6610/api/v1/datasets/87a3346677ee11f0a8f37e63526e5b16 "HTTP/1.1 200 OK"
2025-08-19 13:35:15.827 | INFO     | 91664892a78c4344ba421d3885e35cc6 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/87a3346677ee11f0a8f37e63526e5b16 | 22.972ms
2025-08-19 13:35:20.687 | INFO     | 6fa401bf314e4f80bf74a784bef857f0 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 13:35:20.689 | INFO     | 6fa401bf314e4f80bf74a784bef857f0 | 成功认证Java用户: pythontest
2025-08-19 13:35:20.694 | INFO     | 6fa401bf314e4f80bf74a784bef857f0 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 13:35:20.694 | INFO     | 6fa401bf314e4f80bf74a784bef857f0 | 权限检查通过: user_id=42, permission=knowledge:base:view
2025-08-19 13:35:20.704 | INFO     | 6fa401bf314e4f80bf74a784bef857f0 | HTTP Request: GET http://*************:6610/api/v1/datasets/87a3346677ee11f0a8f37e63526e5b16 "HTTP/1.1 200 OK"
2025-08-19 13:35:20.705 | INFO     | 6fa401bf314e4f80bf74a784bef857f0 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/87a3346677ee11f0a8f37e63526e5b16 | 18.684ms
2025-08-19 13:43:07.706 | INFO     | - | ✅ Java系统数据库连接创建成功
2025-08-19 13:43:08.242 | INFO     | a371df999b264ffeb27e2663f00b8168 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 13:43:08.244 | INFO     | a371df999b264ffeb27e2663f00b8168 | 成功认证Java用户: pythontest
2025-08-19 13:43:08.272 | INFO     | a371df999b264ffeb27e2663f00b8168 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-19 13:43:08.273 | INFO     | a371df999b264ffeb27e2663f00b8168 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 41.826ms
2025-08-19 13:43:10.231 | INFO     | bd388ff306964c0ea22a7608a47af9ec | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 13:43:10.232 | INFO     | bd388ff306964c0ea22a7608a47af9ec | 成功认证Java用户: pythontest
2025-08-19 13:43:10.249 | INFO     | bd388ff306964c0ea22a7608a47af9ec | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 13:43:10.250 | INFO     | bd388ff306964c0ea22a7608a47af9ec | 权限检查通过: user_id=42, permission=knowledge:base:view
2025-08-19 13:43:10.259 | INFO     | bd388ff306964c0ea22a7608a47af9ec | HTTP Request: GET http://*************:6610/api/v1/datasets/87a3346677ee11f0a8f37e63526e5b16 "HTTP/1.1 200 OK"
2025-08-19 13:43:10.260 | INFO     | bd388ff306964c0ea22a7608a47af9ec | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/87a3346677ee11f0a8f37e63526e5b16 | 29.215ms
2025-08-19 13:43:18.961 | INFO     | 85b5b6a849db4479bd2180a98728f3e9 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 13:43:18.962 | INFO     | 85b5b6a849db4479bd2180a98728f3e9 | 成功认证Java用户: pythontest
2025-08-19 13:43:18.987 | INFO     | 85b5b6a849db4479bd2180a98728f3e9 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 13:43:18.988 | INFO     | 85b5b6a849db4479bd2180a98728f3e9 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 13:43:19.003 | INFO     | 85b5b6a849db4479bd2180a98728f3e9 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 13:43:19.005 | INFO     | 85b5b6a849db4479bd2180a98728f3e9 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 44.560ms
2025-08-19 13:43:19.635 | INFO     | 59e5c520205e4f5ab98ea8b2340f879d | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 13:43:19.637 | INFO     | 59e5c520205e4f5ab98ea8b2340f879d | 成功认证Java用户: pythontest
2025-08-19 13:43:19.644 | INFO     | 59e5c520205e4f5ab98ea8b2340f879d | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 13:43:19.645 | INFO     | 59e5c520205e4f5ab98ea8b2340f879d | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 13:43:19.661 | INFO     | 59e5c520205e4f5ab98ea8b2340f879d | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 13:43:19.664 | INFO     | 59e5c520205e4f5ab98ea8b2340f879d | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 28.992ms
2025-08-19 13:43:20.016 | INFO     | 88770995cc9d4da38d029e9559fece5d | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 13:43:20.017 | INFO     | 88770995cc9d4da38d029e9559fece5d | 成功认证Java用户: pythontest
2025-08-19 13:43:20.026 | INFO     | 88770995cc9d4da38d029e9559fece5d | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 13:43:20.027 | INFO     | 88770995cc9d4da38d029e9559fece5d | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 13:43:20.047 | INFO     | 88770995cc9d4da38d029e9559fece5d | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 13:43:20.049 | INFO     | 88770995cc9d4da38d029e9559fece5d | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 32.849ms
2025-08-19 13:43:20.281 | INFO     | c08c12c345a942dda20740bba59a9d8f | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 13:43:20.283 | INFO     | c08c12c345a942dda20740bba59a9d8f | 成功认证Java用户: pythontest
2025-08-19 13:43:20.289 | INFO     | c08c12c345a942dda20740bba59a9d8f | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 13:43:20.290 | INFO     | c08c12c345a942dda20740bba59a9d8f | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 13:43:20.306 | INFO     | c08c12c345a942dda20740bba59a9d8f | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 13:43:20.308 | INFO     | c08c12c345a942dda20740bba59a9d8f | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 27.597ms
2025-08-19 13:43:26.837 | INFO     | ca2feecde8ed414ea6e59b5c934b9db5 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 13:43:26.838 | INFO     | ca2feecde8ed414ea6e59b5c934b9db5 | 成功认证Java用户: pythontest
2025-08-19 13:43:26.864 | INFO     | ca2feecde8ed414ea6e59b5c934b9db5 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-19 13:43:26.866 | INFO     | ca2feecde8ed414ea6e59b5c934b9db5 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 29.359ms
2025-08-19 13:43:26.869 | INFO     | 8008fc4549534a1fbe7fe9baa1c8d0e4 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 13:43:26.870 | INFO     | 8008fc4549534a1fbe7fe9baa1c8d0e4 | 成功认证Java用户: pythontest
2025-08-19 13:43:26.878 | INFO     | 8008fc4549534a1fbe7fe9baa1c8d0e4 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 13:43:26.879 | INFO     | 8008fc4549534a1fbe7fe9baa1c8d0e4 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 13:43:26.896 | INFO     | bf4825ce879c46f4ba99dcc1196d4ecc | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 13:43:26.897 | INFO     | 8008fc4549534a1fbe7fe9baa1c8d0e4 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 13:43:26.900 | INFO     | bf4825ce879c46f4ba99dcc1196d4ecc | 成功认证Java用户: pythontest
2025-08-19 13:43:26.901 | INFO     | 8008fc4549534a1fbe7fe9baa1c8d0e4 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 33.289ms
2025-08-19 13:43:26.909 | INFO     | bf4825ce879c46f4ba99dcc1196d4ecc | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 13:43:26.911 | INFO     | bf4825ce879c46f4ba99dcc1196d4ecc | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-19 13:43:26.928 | INFO     | bf4825ce879c46f4ba99dcc1196d4ecc | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 13:43:26.930 | INFO     | bf4825ce879c46f4ba99dcc1196d4ecc | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 34.392ms
2025-08-19 13:43:33.749 | INFO     | 56176edece24443793642c5a1843f0d6 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 13:43:33.751 | INFO     | 56176edece24443793642c5a1843f0d6 | 成功认证Java用户: pythontest
2025-08-19 13:43:33.764 | INFO     | 56176edece24443793642c5a1843f0d6 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 13:43:33.765 | INFO     | 56176edece24443793642c5a1843f0d6 | 权限检查通过: user_id=42, permission=knowledge:base:create
2025-08-19 13:43:33.766 | INFO     | 56176edece24443793642c5a1843f0d6 | 创建知识库请求数据: {'name': 'pythontest2', 'description': '', 'permission': 'me', 'chunk_method': 'naive', 'parser_config': {'chunk_token_num': 128, 'delimiter': '\\n!?;。；！？', 'html4excel': False, 'layout_recognize': 'DeepDOC', 'auto_keywords': 0, 'auto_questions': 0, 'task_page_size': 12}}
2025-08-19 13:43:33.977 | INFO     | 56176edece24443793642c5a1843f0d6 | HTTP Request: POST http://*************:6610/api/v1/datasets "HTTP/1.1 200 OK"
2025-08-19 13:43:33.979 | INFO     | 56176edece24443793642c5a1843f0d6 | 127.0.0.1       | POST     | 200    | /api/iot/v1/knowledge-base | 229.922ms
2025-08-19 13:43:33.991 | INFO     | 77190c17e3254d148183867526a7bd71 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 13:43:33.993 | INFO     | bdc2b918c1114307bb2660f7283a421d | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 13:43:33.994 | INFO     | 77190c17e3254d148183867526a7bd71 | 成功认证Java用户: pythontest
2025-08-19 13:43:33.998 | INFO     | bdc2b918c1114307bb2660f7283a421d | 成功认证Java用户: pythontest
2025-08-19 13:43:34.008 | INFO     | bdc2b918c1114307bb2660f7283a421d | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 13:43:34.009 | INFO     | bdc2b918c1114307bb2660f7283a421d | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-19 13:43:34.021 | INFO     | 77190c17e3254d148183867526a7bd71 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 13:43:34.022 | INFO     | 77190c17e3254d148183867526a7bd71 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 13:43:34.029 | INFO     | bdc2b918c1114307bb2660f7283a421d | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 13:43:34.030 | INFO     | bdc2b918c1114307bb2660f7283a421d | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 39.055ms
2025-08-19 13:43:34.039 | INFO     | 77190c17e3254d148183867526a7bd71 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 13:43:34.041 | INFO     | 77190c17e3254d148183867526a7bd71 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 50.646ms
2025-08-19 13:43:39.397 | INFO     | 7bb59e5eb8d542039d333778fd3fee12 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 13:43:39.399 | INFO     | 7bb59e5eb8d542039d333778fd3fee12 | 成功认证Java用户: pythontest
2025-08-19 13:43:39.408 | INFO     | 7bb59e5eb8d542039d333778fd3fee12 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 13:43:39.409 | INFO     | 7bb59e5eb8d542039d333778fd3fee12 | 权限检查通过: user_id=42, permission=knowledge:base:update
2025-08-19 13:43:39.427 | INFO     | 7bb59e5eb8d542039d333778fd3fee12 | HTTP Request: PUT http://*************:6610/api/v1/datasets/721d81667cbf11f09259ea5dc8d5776c "HTTP/1.1 200 OK"
2025-08-19 13:43:39.429 | INFO     | 7bb59e5eb8d542039d333778fd3fee12 | 127.0.0.1       | PUT      | 200    | /api/iot/v1/knowledge-base/721d81667cbf11f09259ea5dc8d5776c | 32.888ms
2025-08-19 13:45:11.077 | INFO     | 25eb588450774b21a407dcf4c02ee965 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 13:45:11.078 | INFO     | 7e25b9f9534e45f1ad2b0431990b8c61 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 13:45:11.078 | INFO     | 9afae2d5d12248d0b7cb577333e139d6 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 13:45:11.081 | INFO     | 25eb588450774b21a407dcf4c02ee965 | 成功认证Java用户: pythontest
2025-08-19 13:45:11.081 | INFO     | 7e25b9f9534e45f1ad2b0431990b8c61 | 成功认证Java用户: pythontest
2025-08-19 13:45:11.083 | INFO     | 9afae2d5d12248d0b7cb577333e139d6 | 成功认证Java用户: pythontest
2025-08-19 13:45:11.092 | INFO     | 9afae2d5d12248d0b7cb577333e139d6 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 13:45:11.093 | INFO     | 9afae2d5d12248d0b7cb577333e139d6 | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-19 13:45:11.096 | INFO     | 7e25b9f9534e45f1ad2b0431990b8c61 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 13:45:11.097 | INFO     | 7e25b9f9534e45f1ad2b0431990b8c61 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 13:45:11.101 | INFO     | 25eb588450774b21a407dcf4c02ee965 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-19 13:45:11.104 | INFO     | 25eb588450774b21a407dcf4c02ee965 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 26.809ms
2025-08-19 13:45:11.122 | INFO     | 9afae2d5d12248d0b7cb577333e139d6 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 13:45:11.123 | INFO     | 7e25b9f9534e45f1ad2b0431990b8c61 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 13:45:11.125 | INFO     | 9afae2d5d12248d0b7cb577333e139d6 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 45.799ms
2025-08-19 13:45:11.126 | INFO     | 7e25b9f9534e45f1ad2b0431990b8c61 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 47.625ms
2025-08-19 13:45:24.130 | INFO     | 5002fe6a769c49bba7614485616372b8 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 13:45:24.131 | INFO     | 5002fe6a769c49bba7614485616372b8 | 成功认证Java用户: pythontest
2025-08-19 13:45:24.146 | INFO     | 5002fe6a769c49bba7614485616372b8 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-19 13:45:24.147 | INFO     | 5002fe6a769c49bba7614485616372b8 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 16.855ms
2025-08-19 13:45:24.149 | INFO     | 8746c97dce8d43aa8ff5d401c958aec9 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 13:45:24.150 | INFO     | 8746c97dce8d43aa8ff5d401c958aec9 | 成功认证Java用户: pythontest
2025-08-19 13:45:24.156 | INFO     | 8746c97dce8d43aa8ff5d401c958aec9 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 13:45:24.157 | INFO     | 8746c97dce8d43aa8ff5d401c958aec9 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 13:45:24.173 | INFO     | 8746c97dce8d43aa8ff5d401c958aec9 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 13:45:24.174 | INFO     | 8746c97dce8d43aa8ff5d401c958aec9 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 25.170ms
2025-08-19 13:45:24.177 | INFO     | 7f28d9665da84c02b03ddf0eb100a563 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 13:45:24.178 | INFO     | 7f28d9665da84c02b03ddf0eb100a563 | 成功认证Java用户: pythontest
2025-08-19 13:45:24.184 | INFO     | 7f28d9665da84c02b03ddf0eb100a563 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 13:45:24.184 | INFO     | 7f28d9665da84c02b03ddf0eb100a563 | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-19 13:45:24.199 | INFO     | 7f28d9665da84c02b03ddf0eb100a563 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 13:45:24.200 | INFO     | 7f28d9665da84c02b03ddf0eb100a563 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 23.558ms
2025-08-19 13:45:34.080 | INFO     | 6ef4d9066a5e4d4b95bab1749fa6bdf5 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 13:45:34.081 | INFO     | 23925fcea89b41a09b3dbdbfd8572fdd | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 13:45:34.081 | INFO     | 6ef4d9066a5e4d4b95bab1749fa6bdf5 | 成功认证Java用户: pythontest
2025-08-19 13:45:34.082 | INFO     | 23925fcea89b41a09b3dbdbfd8572fdd | 成功认证Java用户: pythontest
2025-08-19 13:45:34.090 | INFO     | 23925fcea89b41a09b3dbdbfd8572fdd | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 13:45:34.091 | INFO     | 23925fcea89b41a09b3dbdbfd8572fdd | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 13:45:34.098 | INFO     | 6ef4d9066a5e4d4b95bab1749fa6bdf5 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-19 13:45:34.099 | INFO     | 6ef4d9066a5e4d4b95bab1749fa6bdf5 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 18.974ms
2025-08-19 13:45:34.100 | INFO     | 00749d18b8004ef1a86948fe4b888067 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 13:45:34.102 | INFO     | 00749d18b8004ef1a86948fe4b888067 | 成功认证Java用户: pythontest
2025-08-19 13:45:34.106 | INFO     | 23925fcea89b41a09b3dbdbfd8572fdd | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 13:45:34.107 | INFO     | 00749d18b8004ef1a86948fe4b888067 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 13:45:34.108 | INFO     | 00749d18b8004ef1a86948fe4b888067 | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-19 13:45:34.111 | INFO     | 23925fcea89b41a09b3dbdbfd8572fdd | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 30.834ms
2025-08-19 13:45:34.124 | INFO     | 00749d18b8004ef1a86948fe4b888067 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 13:45:34.125 | INFO     | 00749d18b8004ef1a86948fe4b888067 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 24.981ms
2025-08-19 13:45:56.765 | INFO     | 986106df236f4ef79a6bd93cfad9fba2 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 13:45:56.766 | INFO     | 986106df236f4ef79a6bd93cfad9fba2 | 成功认证Java用户: pythontest
2025-08-19 13:45:56.781 | INFO     | 986106df236f4ef79a6bd93cfad9fba2 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-19 13:45:56.782 | INFO     | 986106df236f4ef79a6bd93cfad9fba2 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 17.598ms
2025-08-19 13:45:56.785 | INFO     | f2505917b72d499f9c9b5a17bfc2e974 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 13:45:56.786 | INFO     | f2505917b72d499f9c9b5a17bfc2e974 | 成功认证Java用户: pythontest
2025-08-19 13:45:56.793 | INFO     | f2505917b72d499f9c9b5a17bfc2e974 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 13:45:56.794 | INFO     | f2505917b72d499f9c9b5a17bfc2e974 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 13:45:56.810 | INFO     | f2505917b72d499f9c9b5a17bfc2e974 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 13:45:56.811 | INFO     | f2505917b72d499f9c9b5a17bfc2e974 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 26.792ms
2025-08-19 13:45:56.814 | INFO     | 381a3787d62b463f9bef1eb9c84188ab | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 13:45:56.815 | INFO     | 381a3787d62b463f9bef1eb9c84188ab | 成功认证Java用户: pythontest
2025-08-19 13:45:56.822 | INFO     | 381a3787d62b463f9bef1eb9c84188ab | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 13:45:56.822 | INFO     | 381a3787d62b463f9bef1eb9c84188ab | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-19 13:45:56.839 | INFO     | 381a3787d62b463f9bef1eb9c84188ab | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 13:45:56.840 | INFO     | 381a3787d62b463f9bef1eb9c84188ab | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 26.446ms
2025-08-19 13:46:10.514 | INFO     | 10637e5a44e94ccdb48f7e4fb8a2bf71 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 13:46:10.516 | INFO     | fb15b2be1b3a496d8f4a49358fecfc92 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 13:46:10.517 | INFO     | 10637e5a44e94ccdb48f7e4fb8a2bf71 | 成功认证Java用户: pythontest
2025-08-19 13:46:10.519 | INFO     | fb15b2be1b3a496d8f4a49358fecfc92 | 成功认证Java用户: pythontest
2025-08-19 13:46:10.525 | INFO     | fb15b2be1b3a496d8f4a49358fecfc92 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 13:46:10.526 | INFO     | fb15b2be1b3a496d8f4a49358fecfc92 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 13:46:10.531 | INFO     | 10637e5a44e94ccdb48f7e4fb8a2bf71 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-19 13:46:10.532 | INFO     | 10637e5a44e94ccdb48f7e4fb8a2bf71 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 18.017ms
2025-08-19 13:46:10.534 | INFO     | deeb90e229194b41b447d2abc5a7406f | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 13:46:10.536 | INFO     | deeb90e229194b41b447d2abc5a7406f | 成功认证Java用户: pythontest
2025-08-19 13:46:10.539 | INFO     | fb15b2be1b3a496d8f4a49358fecfc92 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 13:46:10.541 | INFO     | fb15b2be1b3a496d8f4a49358fecfc92 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 25.809ms
2025-08-19 13:46:10.542 | INFO     | deeb90e229194b41b447d2abc5a7406f | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 13:46:10.543 | INFO     | deeb90e229194b41b447d2abc5a7406f | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-19 13:46:10.559 | INFO     | deeb90e229194b41b447d2abc5a7406f | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 13:46:10.560 | INFO     | deeb90e229194b41b447d2abc5a7406f | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 26.265ms
2025-08-19 13:46:22.563 | INFO     | 5ac3f4a2c1454ecb8ecf4f9e48469758 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 13:46:22.564 | INFO     | 5ac3f4a2c1454ecb8ecf4f9e48469758 | 成功认证Java用户: pythontest
2025-08-19 13:46:22.578 | INFO     | 5ac3f4a2c1454ecb8ecf4f9e48469758 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-19 13:46:22.579 | INFO     | 5ac3f4a2c1454ecb8ecf4f9e48469758 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 16.288ms
2025-08-19 13:46:22.581 | INFO     | 8af8322b87c0480184dd50401073f88c | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 13:46:22.583 | INFO     | 8af8322b87c0480184dd50401073f88c | 成功认证Java用户: pythontest
2025-08-19 13:46:22.589 | INFO     | 8af8322b87c0480184dd50401073f88c | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 13:46:22.589 | INFO     | 8af8322b87c0480184dd50401073f88c | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 13:46:22.603 | INFO     | 8af8322b87c0480184dd50401073f88c | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 13:46:22.604 | INFO     | 8af8322b87c0480184dd50401073f88c | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 22.991ms
2025-08-19 13:46:22.606 | INFO     | 6978ea26abfc48b99a11e5d8423c546a | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 13:46:22.607 | INFO     | 6978ea26abfc48b99a11e5d8423c546a | 成功认证Java用户: pythontest
2025-08-19 13:46:22.612 | INFO     | 6978ea26abfc48b99a11e5d8423c546a | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 13:46:22.613 | INFO     | 6978ea26abfc48b99a11e5d8423c546a | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-19 13:46:22.626 | INFO     | 6978ea26abfc48b99a11e5d8423c546a | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 13:46:22.627 | INFO     | 6978ea26abfc48b99a11e5d8423c546a | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 21.489ms
2025-08-19 13:46:35.034 | INFO     | 6a96752e53294aecbcc471bfe9c20cdf | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 13:46:35.035 | INFO     | 1a8f72ed520f4f75b6c27225b8d880b9 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 13:46:35.036 | INFO     | 6a96752e53294aecbcc471bfe9c20cdf | 成功认证Java用户: pythontest
2025-08-19 13:46:35.037 | INFO     | 1a8f72ed520f4f75b6c27225b8d880b9 | 成功认证Java用户: pythontest
2025-08-19 13:46:35.047 | INFO     | 1a8f72ed520f4f75b6c27225b8d880b9 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 13:46:35.048 | INFO     | 1a8f72ed520f4f75b6c27225b8d880b9 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 13:46:35.056 | INFO     | 6a96752e53294aecbcc471bfe9c20cdf | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-19 13:46:35.057 | INFO     | 6a96752e53294aecbcc471bfe9c20cdf | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 23.597ms
2025-08-19 13:46:35.061 | INFO     | eb118cf64c23461f84e312aebfee8d9d | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 13:46:35.063 | INFO     | eb118cf64c23461f84e312aebfee8d9d | 成功认证Java用户: pythontest
2025-08-19 13:46:35.064 | INFO     | 1a8f72ed520f4f75b6c27225b8d880b9 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 13:46:35.067 | INFO     | 1a8f72ed520f4f75b6c27225b8d880b9 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 31.882ms
2025-08-19 13:46:35.073 | INFO     | eb118cf64c23461f84e312aebfee8d9d | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 13:46:35.074 | INFO     | eb118cf64c23461f84e312aebfee8d9d | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-19 13:46:35.090 | INFO     | eb118cf64c23461f84e312aebfee8d9d | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 13:46:35.091 | INFO     | eb118cf64c23461f84e312aebfee8d9d | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 30.738ms
2025-08-19 13:46:46.000 | INFO     | 8d4e0fea63be4d7998cce54337d5120b | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 13:46:46.002 | INFO     | 8d4e0fea63be4d7998cce54337d5120b | 成功认证Java用户: pythontest
2025-08-19 13:46:46.015 | INFO     | 8d4e0fea63be4d7998cce54337d5120b | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-19 13:46:46.017 | INFO     | 8d4e0fea63be4d7998cce54337d5120b | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 16.788ms
2025-08-19 13:46:46.019 | INFO     | c083816ac9fe4ac48df0de3839bca114 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 13:46:46.020 | INFO     | c083816ac9fe4ac48df0de3839bca114 | 成功认证Java用户: pythontest
2025-08-19 13:46:46.026 | INFO     | c083816ac9fe4ac48df0de3839bca114 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 13:46:46.026 | INFO     | c083816ac9fe4ac48df0de3839bca114 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 13:46:46.041 | INFO     | c083816ac9fe4ac48df0de3839bca114 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 13:46:46.042 | INFO     | c083816ac9fe4ac48df0de3839bca114 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 22.619ms
2025-08-19 13:46:46.044 | INFO     | 279ebdf7798c44458987215a697dec6e | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 13:46:46.046 | INFO     | 279ebdf7798c44458987215a697dec6e | 成功认证Java用户: pythontest
2025-08-19 13:46:46.050 | INFO     | 279ebdf7798c44458987215a697dec6e | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 13:46:46.051 | INFO     | 279ebdf7798c44458987215a697dec6e | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-19 13:46:46.065 | INFO     | 279ebdf7798c44458987215a697dec6e | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 13:46:46.066 | INFO     | 279ebdf7798c44458987215a697dec6e | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 22.450ms
2025-08-19 13:46:54.163 | INFO     | 0eae73de9a074a2b9d657dbdee5cd7f4 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 13:46:54.165 | INFO     | 1fff5d0cce1e41fe80e1dbbe1e8bbae1 | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 13:46:54.165 | INFO     | 0eae73de9a074a2b9d657dbdee5cd7f4 | 成功认证Java用户: pythontest
2025-08-19 13:46:54.166 | INFO     | 1fff5d0cce1e41fe80e1dbbe1e8bbae1 | 成功认证Java用户: pythontest
2025-08-19 13:46:54.173 | INFO     | 1fff5d0cce1e41fe80e1dbbe1e8bbae1 | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 13:46:54.173 | INFO     | 1fff5d0cce1e41fe80e1dbbe1e8bbae1 | 权限检查通过: user_id=42, permission=knowledge:base:list
2025-08-19 13:46:54.179 | INFO     | 0eae73de9a074a2b9d657dbdee5cd7f4 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=1 "HTTP/1.1 200 OK"
2025-08-19 13:46:54.180 | INFO     | 0eae73de9a074a2b9d657dbdee5cd7f4 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/health | 16.720ms
2025-08-19 13:46:54.183 | INFO     | 96ad188349144ddc99e3ca8c0094c00b | JWT标准验证成功，获取UUID: a600d04c-72d7-46fe-9e31-2dc9396091bf
2025-08-19 13:46:54.184 | INFO     | 96ad188349144ddc99e3ca8c0094c00b | 成功认证Java用户: pythontest
2025-08-19 13:46:54.187 | INFO     | 1fff5d0cce1e41fe80e1dbbe1e8bbae1 | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=30&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 13:46:54.189 | INFO     | 1fff5d0cce1e41fe80e1dbbe1e8bbae1 | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/list/page=1&page_size=30&orderby=create_time&desc=true | 23.657ms
2025-08-19 13:46:54.190 | INFO     | 96ad188349144ddc99e3ca8c0094c00b | 获取用户权限成功: user_id=42, is_admin=False, permissions=18
2025-08-19 13:46:54.190 | INFO     | 96ad188349144ddc99e3ca8c0094c00b | 权限检查通过: user_id=42, permission=knowledge:base:stats
2025-08-19 13:46:54.205 | INFO     | 96ad188349144ddc99e3ca8c0094c00b | HTTP Request: GET http://*************:6610/api/v1/datasets?page=1&page_size=100&orderby=create_time&desc=true "HTTP/1.1 200 OK"
2025-08-19 13:46:54.206 | INFO     | 96ad188349144ddc99e3ca8c0094c00b | 127.0.0.1       | GET      | 200    | /api/iot/v1/knowledge-base/stats/overview | 24.160ms
