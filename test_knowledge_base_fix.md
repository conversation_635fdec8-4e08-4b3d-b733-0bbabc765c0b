# 知识库管理功能修复测试指南

## 修复内容总结

### 1. 前端API接口类型定义修复
- ✅ 更新 `UpdateKnowledgeBaseParams` 接口，添加 `description` 和 `pagerank` 字段
- ✅ 更新 `CreateKnowledgeBaseParams` 接口，添加 `pagerank` 字段
- ✅ 移除编辑时不允许修改的字段（`embedding_model`、`chunk_method`）

### 2. 后端数据模型修复
- ✅ 更新 `KnowledgeBaseUpdate` 模型，添加 `description` 和 `pagerank` 字段支持
- ✅ 移除不允许编辑的字段，确保数据安全

### 3. 前端表单优化
- ✅ 编辑表单：只显示 `name`、`description`、`pagerank` 字段
- ✅ 新建表单：只显示 `name`、`description`、`pagerank` 字段
- ✅ 移除 `chunk_method` 和 `permission` 字段选择，使用后端默认值
- ✅ 清理不需要的代码和导入

## 测试步骤

### 1. 新建知识库测试
1. 打开知识库管理页面
2. 点击"创建知识库"按钮
3. 验证对话框只显示以下字段：
   - ✅ 知识库名称（必填）
   - ✅ 描述（可选）
   - ✅ 页面排名（滑块，0-100）
4. 填写信息并提交
5. 验证创建成功，后端使用默认值：
   - `permission`: 'me'（私有）
   - `chunk_method`: 'naive'（通用分块）
   - `embedding_model`: 系统默认模型

### 2. 编辑知识库测试
1. 在知识库列表中点击"编辑"按钮
2. 验证对话框只显示以下字段：
   - ✅ 知识库名称（可编辑）
   - ✅ 描述（可编辑）
   - ✅ 页面排名（可编辑）
3. 修改信息并提交
4. 验证更新成功，不可编辑字段保持原值：
   - `embedding_model` 不变
   - `chunk_method` 不变
   - `permission` 不变

### 3. 查看知识库测试
1. 在知识库列表中点击"查看"按钮
2. 验证详情对话框显示完整信息：
   - ✅ 知识库名称
   - ✅ 描述
   - ✅ 嵌入模型
   - ✅ 分块方法
   - ✅ 访问权限
   - ✅ 页面排名
   - ✅ 统计信息（文档数、分块数、Token数）
   - ✅ 时间信息（创建时间、更新时间）

## 预期结果

### ✅ 成功指标
1. 新建知识库时只需填写核心信息，界面简洁
2. 编辑知识库时只能修改安全字段，避免影响已有数据
3. 查看功能正常显示所有信息
4. 前后端数据传递正确，无类型错误
5. API调用成功，无网络错误

### ❌ 失败指标
1. 表单显示多余字段（chunk_method、permission）
2. 编辑时可以修改不应该修改的字段
3. 新建或更新时出现API错误
4. 前端TypeScript类型错误
5. 数据显示不完整或错误

## 技术细节

### API接口变更
```typescript
// 更新前
interface UpdateKnowledgeBaseParams {
  name?: string;
  embedding_model?: string;
  chunk_method?: string;
}

// 更新后
interface UpdateKnowledgeBaseParams {
  name?: string;
  description?: string;
  pagerank?: number;
}
```

### 后端模型变更
```python
# 更新前
class KnowledgeBaseUpdate(BaseModel):
    name: Optional[str] = None
    embedding_model: Optional[str] = None
    chunk_method: Optional[str] = None

# 更新后
class KnowledgeBaseUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    pagerank: Optional[int] = Field(None, ge=0, le=100)
```

### 前端表单变更
- 移除分块方法选择器
- 移除访问权限单选框
- 保留页面排名滑块
- 简化表单验证规则

## 注意事项

1. **数据兼容性**：现有知识库数据不受影响
2. **默认值处理**：新建时使用RAGFlow系统默认值
3. **权限控制**：编辑权限仍然受后端权限系统控制
4. **错误处理**：保持原有的错误处理机制
5. **向后兼容**：API接口保持向后兼容

## 如果测试失败

### 常见问题排查
1. **TypeScript错误**：检查接口定义是否正确
2. **API调用失败**：检查后端服务是否正常运行
3. **字段显示异常**：检查Vue组件模板是否正确修改
4. **数据传递错误**：检查前后端字段映射是否一致
5. **权限问题**：确认用户有相应的操作权限

### 回滚方案
如果修复导致问题，可以：
1. 恢复原始的接口定义
2. 恢复原始的Vue组件代码
3. 恢复原始的后端模型定义
4. 重新部署服务
